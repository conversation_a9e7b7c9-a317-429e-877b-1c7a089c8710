// Comprehensive Seed Data for Asset Modules
// Production-ready examples showcasing all module capabilities

import { AssetModule, ModuleField, ModuleLogicNode, ModuleValidationRule } from '@/lib/types/asset-modules';

// IT Equipment Module - Comprehensive computer/server tracking
export const itEquipmentModule: AssetModule = {
  id: 'it-equipment-v2',
  name: 'IT Equipment Management',
  description: 'Comprehensive tracking and management of computers, servers, and IT hardware with lifecycle management, warranty tracking, and maintenance scheduling.',
  category: 'hardware',
  version: '2.1.0',
  author: 'WizeAssets Team',
  tags: ['hardware', 'computers', 'servers', 'warranty', 'maintenance'],
  isActive: true,
  isPublic: true,
  createdAt: new Date('2024-01-15'),
  updatedAt: new Date('2024-07-10'),
  
  fields: [
    {
      id: 'asset-tag',
      name: 'assetTag',
      type: 'text',
      label: 'Asset Tag',
      description: 'Unique identifier for the IT equipment',
      required: true,
      validation: {
        pattern: '^IT-[0-9]{6}$',
        min: 9,
        max: 9
      },
      placeholder: 'IT-123456',
      helpText: 'Format: IT-XXXXXX (6 digits)'
    },
    {
      id: 'device-type',
      name: 'deviceType',
      type: 'select',
      label: 'Device Type',
      description: 'Category of IT equipment',
      required: true,
      validation: {
        options: ['Desktop', 'Laptop', 'Server', 'Network Equipment', 'Printer', 'Monitor', 'Mobile Device', 'Tablet']
      },
      placeholder: 'Select device type'
    },
    {
      id: 'manufacturer',
      name: 'manufacturer',
      type: 'select',
      label: 'Manufacturer',
      description: 'Equipment manufacturer',
      required: true,
      validation: {
        options: ['Dell', 'HP', 'Lenovo', 'Apple', 'Microsoft', 'Cisco', 'IBM', 'Asus', 'Acer', 'Other']
      }
    },
    {
      id: 'model',
      name: 'model',
      type: 'text',
      label: 'Model',
      description: 'Specific model number or name',
      required: true,
      validation: {
        min: 2,
        max: 100
      },
      placeholder: 'e.g., OptiPlex 7090, MacBook Pro 16"'
    },
    {
      id: 'serial-number',
      name: 'serialNumber',
      type: 'text',
      label: 'Serial Number',
      description: 'Manufacturer serial number',
      required: true,
      validation: {
        min: 5,
        max: 50
      },
      placeholder: 'Enter serial number'
    },
    {
      id: 'specifications',
      name: 'specifications',
      type: 'text',
      label: 'Specifications',
      description: 'Key technical specifications (CPU, RAM, Storage)',
      required: false,
      validation: {
        max: 500
      },
      placeholder: 'e.g., Intel i7-11700, 16GB RAM, 512GB SSD'
    },
    {
      id: 'operating-system',
      name: 'operatingSystem',
      type: 'select',
      label: 'Operating System',
      description: 'Installed operating system',
      required: false,
      validation: {
        options: ['Windows 11', 'Windows 10', 'macOS Sonoma', 'macOS Ventura', 'Ubuntu 22.04', 'CentOS 8', 'RHEL 9', 'Other', 'None']
      }
    },
    {
      id: 'purchase-date',
      name: 'purchaseDate',
      type: 'date',
      label: 'Purchase Date',
      description: 'Date when equipment was purchased',
      required: true,
      validation: {}
    },
    {
      id: 'purchase-price',
      name: 'purchasePrice',
      type: 'number',
      label: 'Purchase Price',
      description: 'Original purchase price in USD',
      required: false,
      validation: {
        min: 0,
        max: 100000
      },
      placeholder: '0.00'
    },
    {
      id: 'warranty-expiry',
      name: 'warrantyExpiry',
      type: 'date',
      label: 'Warranty Expiry',
      description: 'Date when warranty expires',
      required: false,
      validation: {}
    },
    {
      id: 'assigned-user',
      name: 'assignedUser',
      type: 'text',
      label: 'Assigned User',
      description: 'Employee currently using this equipment',
      required: false,
      validation: {
        max: 100
      },
      placeholder: 'Employee name or ID'
    },
    {
      id: 'location',
      name: 'location',
      type: 'text',
      label: 'Location',
      description: 'Physical location of the equipment',
      required: true,
      validation: {
        max: 200
      },
      placeholder: 'Building, Floor, Room'
    },
    {
      id: 'status',
      name: 'status',
      type: 'select',
      label: 'Status',
      description: 'Current operational status',
      required: true,
      validation: {
        options: ['Active', 'Inactive', 'In Repair', 'Retired', 'Lost/Stolen', 'Disposed']
      }
    },
    {
      id: 'last-maintenance',
      name: 'lastMaintenance',
      type: 'date',
      label: 'Last Maintenance',
      description: 'Date of last maintenance or service',
      required: false,
      validation: {}
    },
    {
      id: 'next-maintenance',
      name: 'nextMaintenance',
      type: 'date',
      label: 'Next Maintenance',
      description: 'Scheduled date for next maintenance',
      required: false,
      validation: {}
    }
  ],

  logic: {
    nodes: [
      {
        id: 'warranty-check',
        type: 'condition',
        name: 'Warranty Status Check',
        description: 'Check if warranty is expiring soon',
        position: { x: 100, y: 100 },
        config: {
          condition: 'warrantyExpiry <= today + 30 days',
          trueAction: 'trigger-warranty-alert',
          falseAction: 'continue'
        }
      },
      {
        id: 'maintenance-schedule',
        type: 'calculation',
        name: 'Calculate Next Maintenance',
        description: 'Auto-calculate next maintenance date',
        position: { x: 300, y: 100 },
        config: {
          formula: 'lastMaintenance + 6 months',
          targetField: 'nextMaintenance'
        }
      },
      {
        id: 'depreciation-calc',
        type: 'calculation',
        name: 'Calculate Depreciation',
        description: 'Calculate current asset value',
        position: { x: 500, y: 100 },
        config: {
          formula: 'purchasePrice * (1 - (age / 5))',
          targetField: 'currentValue'
        }
      }
    ],
    edges: [
      {
        id: 'edge-1',
        source: 'warranty-check',
        target: 'maintenance-schedule',
        label: 'Continue'
      },
      {
        id: 'edge-2',
        source: 'maintenance-schedule',
        target: 'depreciation-calc',
        label: 'Calculate'
      }
    ]
  },

  validation: {
    rules: [
      {
        id: 'serial-unique',
        field: 'serialNumber',
        type: 'unique',
        message: 'Serial number must be unique across all IT equipment',
        severity: 'error'
      },
      {
        id: 'warranty-future',
        field: 'warrantyExpiry',
        type: 'custom',
        message: 'Warranty expiry should be after purchase date',
        severity: 'warning',
        condition: 'warrantyExpiry > purchaseDate'
      },
      {
        id: 'maintenance-logic',
        field: 'nextMaintenance',
        type: 'custom',
        message: 'Next maintenance should be after last maintenance',
        severity: 'error',
        condition: 'nextMaintenance > lastMaintenance'
      }
    ]
  },

  rendering: {
    layout: 'grid',
    sections: [
      {
        id: 'basic-info',
        title: 'Basic Information',
        fields: ['assetTag', 'deviceType', 'manufacturer', 'model', 'serialNumber'],
        columns: 2
      },
      {
        id: 'technical',
        title: 'Technical Details',
        fields: ['specifications', 'operatingSystem'],
        columns: 1
      },
      {
        id: 'financial',
        title: 'Financial Information',
        fields: ['purchaseDate', 'purchasePrice', 'warrantyExpiry'],
        columns: 3
      },
      {
        id: 'assignment',
        title: 'Assignment & Location',
        fields: ['assignedUser', 'location', 'status'],
        columns: 2
      },
      {
        id: 'maintenance',
        title: 'Maintenance Schedule',
        fields: ['lastMaintenance', 'nextMaintenance'],
        columns: 2
      }
    ]
  },

  permissions: {
    view: ['all'],
    edit: ['admin', 'it-manager'],
    delete: ['admin'],
    export: ['admin', 'it-manager', 'auditor']
  },

  integrations: {
    apis: ['asset-tracking', 'warranty-service', 'maintenance-scheduler'],
    webhooks: ['warranty-expiry-alert', 'maintenance-due'],
    exports: ['csv', 'pdf', 'excel']
  },

  metadata: {
    documentation: 'Complete IT equipment tracking with automated workflows',
    changeLog: [
      { version: '2.1.0', date: '2024-07-10', changes: 'Added maintenance scheduling' },
      { version: '2.0.0', date: '2024-06-01', changes: 'Major update with warranty tracking' }
    ],
    usage: {
      totalAssets: 1247,
      activeUsers: 23,
      lastUsed: new Date('2024-07-10')
    }
  }
};

// Software License Module - Comprehensive software asset management
export const softwareLicenseModule: AssetModule = {
  id: 'software-license-v1',
  name: 'Software License Management',
  description: 'Track software licenses, compliance, renewals, and usage across the organization with automated compliance monitoring.',
  category: 'software',
  version: '1.5.0',
  author: 'WizeAssets Team',
  tags: ['software', 'licenses', 'compliance', 'renewals'],
  isActive: true,
  isPublic: true,
  createdAt: new Date('2024-02-01'),
  updatedAt: new Date('2024-07-10'),

  fields: [
    {
      id: 'software-name',
      name: 'softwareName',
      type: 'text',
      label: 'Software Name',
      description: 'Name of the software application',
      required: true,
      validation: {
        min: 2,
        max: 100
      },
      placeholder: 'e.g., Microsoft Office 365, Adobe Creative Suite'
    },
    {
      id: 'vendor',
      name: 'vendor',
      type: 'select',
      label: 'Vendor',
      description: 'Software vendor or publisher',
      required: true,
      validation: {
        options: ['Microsoft', 'Adobe', 'Oracle', 'SAP', 'Salesforce', 'Google', 'Atlassian', 'VMware', 'Other']
      }
    },
    {
      id: 'license-type',
      name: 'licenseType',
      type: 'select',
      label: 'License Type',
      description: 'Type of software license',
      required: true,
      validation: {
        options: ['Perpetual', 'Subscription', 'Volume', 'OEM', 'Open Source', 'Trial', 'Educational']
      }
    },
    {
      id: 'license-key',
      name: 'licenseKey',
      type: 'text',
      label: 'License Key',
      description: 'Software license key or activation code',
      required: false,
      validation: {
        max: 200
      },
      placeholder: 'Enter license key (will be encrypted)'
    },
    {
      id: 'total-licenses',
      name: 'totalLicenses',
      type: 'number',
      label: 'Total Licenses',
      description: 'Total number of licenses purchased',
      required: true,
      validation: {
        min: 1,
        max: 10000
      },
      placeholder: '1'
    },
    {
      id: 'used-licenses',
      name: 'usedLicenses',
      type: 'number',
      label: 'Used Licenses',
      description: 'Number of licenses currently in use',
      required: true,
      validation: {
        min: 0,
        max: 10000
      },
      placeholder: '0'
    },
    {
      id: 'purchase-date',
      name: 'purchaseDate',
      type: 'date',
      label: 'Purchase Date',
      description: 'Date when licenses were purchased',
      required: true,
      validation: {}
    },
    {
      id: 'expiry-date',
      name: 'expiryDate',
      type: 'date',
      label: 'Expiry Date',
      description: 'Date when licenses expire (for subscriptions)',
      required: false,
      validation: {}
    },
    {
      id: 'annual-cost',
      name: 'annualCost',
      type: 'number',
      label: 'Annual Cost',
      description: 'Annual licensing cost in USD',
      required: false,
      validation: {
        min: 0,
        max: 1000000
      },
      placeholder: '0.00'
    },
    {
      id: 'cost-per-license',
      name: 'costPerLicense',
      type: 'number',
      label: 'Cost Per License',
      description: 'Cost per individual license',
      required: false,
      validation: {
        min: 0,
        max: 10000
      },
      placeholder: '0.00'
    },
    {
      id: 'renewal-date',
      name: 'renewalDate',
      type: 'date',
      label: 'Renewal Date',
      description: 'Next renewal date for subscription licenses',
      required: false,
      validation: {}
    },
    {
      id: 'compliance-status',
      name: 'complianceStatus',
      type: 'select',
      label: 'Compliance Status',
      description: 'Current license compliance status',
      required: true,
      validation: {
        options: ['Compliant', 'Over-licensed', 'Under-licensed', 'Expired', 'Unknown']
      }
    },
    {
      id: 'installation-count',
      name: 'installationCount',
      type: 'number',
      label: 'Installation Count',
      description: 'Number of installations detected',
      required: false,
      validation: {
        min: 0,
        max: 10000
      },
      placeholder: '0'
    },
    {
      id: 'support-level',
      name: 'supportLevel',
      type: 'select',
      label: 'Support Level',
      description: 'Level of vendor support included',
      required: false,
      validation: {
        options: ['Basic', 'Standard', 'Premium', 'Enterprise', 'None']
      }
    }
  ],

  logic: {
    nodes: [
      {
        id: 'compliance-check',
        type: 'condition',
        name: 'License Compliance Check',
        description: 'Check if usage exceeds available licenses',
        position: { x: 100, y: 100 },
        config: {
          condition: 'usedLicenses > totalLicenses',
          trueAction: 'set-over-licensed',
          falseAction: 'check-under-usage'
        }
      },
      {
        id: 'renewal-alert',
        type: 'condition',
        name: 'Renewal Alert',
        description: 'Alert for upcoming renewals',
        position: { x: 300, y: 100 },
        config: {
          condition: 'renewalDate <= today + 60 days',
          trueAction: 'trigger-renewal-alert',
          falseAction: 'continue'
        }
      },
      {
        id: 'cost-calculation',
        type: 'calculation',
        name: 'Calculate Cost Per License',
        description: 'Auto-calculate cost per license',
        position: { x: 500, y: 100 },
        config: {
          formula: 'annualCost / totalLicenses',
          targetField: 'costPerLicense'
        }
      }
    ],
    edges: [
      {
        id: 'edge-1',
        source: 'compliance-check',
        target: 'renewal-alert',
        label: 'Continue'
      },
      {
        id: 'edge-2',
        source: 'renewal-alert',
        target: 'cost-calculation',
        label: 'Calculate'
      }
    ]
  },

  validation: {
    rules: [
      {
        id: 'usage-limit',
        field: 'usedLicenses',
        type: 'custom',
        message: 'Used licenses cannot exceed total licenses',
        severity: 'error',
        condition: 'usedLicenses <= totalLicenses'
      },
      {
        id: 'expiry-future',
        field: 'expiryDate',
        type: 'custom',
        message: 'Expiry date should be after purchase date',
        severity: 'warning',
        condition: 'expiryDate > purchaseDate'
      }
    ]
  },

  rendering: {
    layout: 'tabs',
    sections: [
      {
        id: 'license-info',
        title: 'License Information',
        fields: ['softwareName', 'vendor', 'licenseType', 'licenseKey'],
        columns: 2
      },
      {
        id: 'usage',
        title: 'Usage & Compliance',
        fields: ['totalLicenses', 'usedLicenses', 'installationCount', 'complianceStatus'],
        columns: 2
      },
      {
        id: 'financial',
        title: 'Financial Details',
        fields: ['purchaseDate', 'annualCost', 'costPerLicense'],
        columns: 3
      },
      {
        id: 'renewal',
        title: 'Renewal & Support',
        fields: ['expiryDate', 'renewalDate', 'supportLevel'],
        columns: 3
      }
    ]
  },

  permissions: {
    view: ['all'],
    edit: ['admin', 'it-manager', 'procurement'],
    delete: ['admin'],
    export: ['admin', 'it-manager', 'auditor', 'finance']
  },

  integrations: {
    apis: ['license-scanner', 'vendor-portal', 'procurement-system'],
    webhooks: ['renewal-alert', 'compliance-violation'],
    exports: ['csv', 'pdf', 'compliance-report']
  },

  metadata: {
    documentation: 'Comprehensive software license management with compliance monitoring',
    changeLog: [
      { version: '1.5.0', date: '2024-07-10', changes: 'Added installation count tracking' },
      { version: '1.4.0', date: '2024-05-15', changes: 'Enhanced compliance monitoring' }
    ],
    usage: {
      totalAssets: 342,
      activeUsers: 15,
      lastUsed: new Date('2024-07-10')
    }
  }
};

// Vehicle Fleet Module - Comprehensive vehicle management
export const vehicleFleetModule: AssetModule = {
  id: 'vehicle-fleet-v1',
  name: 'Vehicle Fleet Management',
  description: 'Complete vehicle fleet tracking with maintenance schedules, fuel management, driver assignments, and compliance monitoring.',
  category: 'vehicle',
  version: '1.3.0',
  author: 'WizeAssets Team',
  tags: ['vehicles', 'fleet', 'maintenance', 'fuel', 'compliance'],
  isActive: true,
  isPublic: true,
  createdAt: new Date('2024-03-01'),
  updatedAt: new Date('2024-07-10'),

  fields: [
    {
      id: 'vehicle-id',
      name: 'vehicleId',
      type: 'text',
      label: 'Vehicle ID',
      description: 'Unique fleet identifier',
      required: true,
      validation: {
        pattern: '^VH-[0-9]{4}$',
        min: 7,
        max: 7
      },
      placeholder: 'VH-1234'
    },
    {
      id: 'license-plate',
      name: 'licensePlate',
      type: 'text',
      label: 'License Plate',
      description: 'Vehicle license plate number',
      required: true,
      validation: {
        min: 3,
        max: 15
      },
      placeholder: 'ABC-1234'
    },
    {
      id: 'make',
      name: 'make',
      type: 'select',
      label: 'Make',
      description: 'Vehicle manufacturer',
      required: true,
      validation: {
        options: ['Ford', 'Chevrolet', 'Toyota', 'Honda', 'Nissan', 'BMW', 'Mercedes-Benz', 'Volkswagen', 'Hyundai', 'Other']
      }
    },
    {
      id: 'model',
      name: 'model',
      type: 'text',
      label: 'Model',
      description: 'Vehicle model',
      required: true,
      validation: {
        min: 1,
        max: 50
      },
      placeholder: 'e.g., F-150, Camry, Accord'
    },
    {
      id: 'year',
      name: 'year',
      type: 'number',
      label: 'Year',
      description: 'Manufacturing year',
      required: true,
      validation: {
        min: 1990,
        max: 2025
      },
      placeholder: '2024'
    },
    {
      id: 'vin',
      name: 'vin',
      type: 'text',
      label: 'VIN',
      description: 'Vehicle Identification Number',
      required: true,
      validation: {
        min: 17,
        max: 17,
        pattern: '^[A-HJ-NPR-Z0-9]{17}$'
      },
      placeholder: '17-character VIN'
    },
    {
      id: 'vehicle-type',
      name: 'vehicleType',
      type: 'select',
      label: 'Vehicle Type',
      description: 'Category of vehicle',
      required: true,
      validation: {
        options: ['Sedan', 'SUV', 'Truck', 'Van', 'Motorcycle', 'Bus', 'Heavy Equipment', 'Trailer']
      }
    },
    {
      id: 'fuel-type',
      name: 'fuelType',
      type: 'select',
      label: 'Fuel Type',
      description: 'Type of fuel used',
      required: true,
      validation: {
        options: ['Gasoline', 'Diesel', 'Electric', 'Hybrid', 'CNG', 'LPG', 'Hydrogen']
      }
    },
    {
      id: 'current-mileage',
      name: 'currentMileage',
      type: 'number',
      label: 'Current Mileage',
      description: 'Current odometer reading',
      required: true,
      validation: {
        min: 0,
        max: 1000000
      },
      placeholder: '0'
    },
    {
      id: 'assigned-driver',
      name: 'assignedDriver',
      type: 'text',
      label: 'Assigned Driver',
      description: 'Primary driver assigned to vehicle',
      required: false,
      validation: {
        max: 100
      },
      placeholder: 'Driver name or employee ID'
    },
    {
      id: 'department',
      name: 'department',
      type: 'select',
      label: 'Department',
      description: 'Department responsible for vehicle',
      required: true,
      validation: {
        options: ['Sales', 'Delivery', 'Maintenance', 'Executive', 'Security', 'General Pool']
      }
    },
    {
      id: 'insurance-policy',
      name: 'insurancePolicy',
      type: 'text',
      label: 'Insurance Policy',
      description: 'Insurance policy number',
      required: true,
      validation: {
        max: 50
      },
      placeholder: 'Policy number'
    },
    {
      id: 'insurance-expiry',
      name: 'insuranceExpiry',
      type: 'date',
      label: 'Insurance Expiry',
      description: 'Insurance policy expiration date',
      required: true,
      validation: {}
    },
    {
      id: 'registration-expiry',
      name: 'registrationExpiry',
      type: 'date',
      label: 'Registration Expiry',
      description: 'Vehicle registration expiration date',
      required: true,
      validation: {}
    },
    {
      id: 'last-service',
      name: 'lastService',
      type: 'date',
      label: 'Last Service',
      description: 'Date of last maintenance service',
      required: false,
      validation: {}
    },
    {
      id: 'next-service',
      name: 'nextService',
      type: 'date',
      label: 'Next Service',
      description: 'Scheduled next service date',
      required: false,
      validation: {}
    },
    {
      id: 'service-interval',
      name: 'serviceInterval',
      type: 'number',
      label: 'Service Interval (miles)',
      description: 'Miles between service intervals',
      required: false,
      validation: {
        min: 1000,
        max: 50000
      },
      placeholder: '5000'
    },
    {
      id: 'status',
      name: 'status',
      type: 'select',
      label: 'Status',
      description: 'Current vehicle status',
      required: true,
      validation: {
        options: ['Active', 'In Service', 'Out of Service', 'Retired', 'Accident', 'Sold']
      }
    }
  ],

  logic: {
    nodes: [
      {
        id: 'service-due-check',
        type: 'condition',
        name: 'Service Due Check',
        description: 'Check if service is due based on mileage or date',
        position: { x: 100, y: 100 },
        config: {
          condition: 'currentMileage >= (lastServiceMileage + serviceInterval) OR nextService <= today',
          trueAction: 'trigger-service-alert',
          falseAction: 'continue'
        }
      },
      {
        id: 'insurance-expiry-check',
        type: 'condition',
        name: 'Insurance Expiry Check',
        description: 'Alert for insurance renewal',
        position: { x: 300, y: 100 },
        config: {
          condition: 'insuranceExpiry <= today + 30 days',
          trueAction: 'trigger-insurance-alert',
          falseAction: 'continue'
        }
      },
      {
        id: 'registration-check',
        type: 'condition',
        name: 'Registration Check',
        description: 'Alert for registration renewal',
        position: { x: 500, y: 100 },
        config: {
          condition: 'registrationExpiry <= today + 30 days',
          trueAction: 'trigger-registration-alert',
          falseAction: 'continue'
        }
      }
    ],
    edges: [
      {
        id: 'edge-1',
        source: 'service-due-check',
        target: 'insurance-expiry-check',
        label: 'Continue'
      },
      {
        id: 'edge-2',
        source: 'insurance-expiry-check',
        target: 'registration-check',
        label: 'Continue'
      }
    ]
  },

  validation: {
    rules: [
      {
        id: 'vin-unique',
        field: 'vin',
        type: 'unique',
        message: 'VIN must be unique across all vehicles',
        severity: 'error'
      },
      {
        id: 'license-unique',
        field: 'licensePlate',
        type: 'unique',
        message: 'License plate must be unique',
        severity: 'error'
      },
      {
        id: 'future-expiry',
        field: 'insuranceExpiry',
        type: 'custom',
        message: 'Insurance expiry should be in the future',
        severity: 'warning',
        condition: 'insuranceExpiry > today'
      }
    ]
  },

  rendering: {
    layout: 'accordion',
    sections: [
      {
        id: 'vehicle-info',
        title: 'Vehicle Information',
        fields: ['vehicleId', 'licensePlate', 'make', 'model', 'year', 'vin', 'vehicleType', 'fuelType'],
        columns: 2
      },
      {
        id: 'assignment',
        title: 'Assignment & Usage',
        fields: ['assignedDriver', 'department', 'currentMileage', 'status'],
        columns: 2
      },
      {
        id: 'compliance',
        title: 'Insurance & Registration',
        fields: ['insurancePolicy', 'insuranceExpiry', 'registrationExpiry'],
        columns: 3
      },
      {
        id: 'maintenance',
        title: 'Maintenance Schedule',
        fields: ['lastService', 'nextService', 'serviceInterval'],
        columns: 3
      }
    ]
  },

  permissions: {
    view: ['all'],
    edit: ['admin', 'fleet-manager'],
    delete: ['admin'],
    export: ['admin', 'fleet-manager', 'finance']
  },

  integrations: {
    apis: ['gps-tracking', 'fuel-card', 'maintenance-scheduler'],
    webhooks: ['service-due', 'insurance-expiry', 'registration-expiry'],
    exports: ['csv', 'pdf', 'fleet-report']
  },

  metadata: {
    documentation: 'Complete vehicle fleet management with compliance tracking',
    changeLog: [
      { version: '1.3.0', date: '2024-07-10', changes: 'Added fuel type tracking' },
      { version: '1.2.0', date: '2024-05-01', changes: 'Enhanced maintenance scheduling' }
    ],
    usage: {
      totalAssets: 156,
      activeUsers: 8,
      lastUsed: new Date('2024-07-10')
    }
  }
};

// Export all seed modules
export const seedModules: AssetModule[] = [
  itEquipmentModule,
  softwareLicenseModule,
  vehicleFleetModule
];

// Helper function to get module by ID
export function getModuleById(id: string): AssetModule | undefined {
  return seedModules.find(module => module.id === id);
}

// Helper function to get modules by category
export function getModulesByCategory(category: string): AssetModule[] {
  return seedModules.filter(module => module.category === category);
}
