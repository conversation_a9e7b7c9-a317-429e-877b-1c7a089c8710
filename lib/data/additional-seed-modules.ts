// Additional Comprehensive Seed Modules
// More production-ready examples for different asset categories

import { AssetModule } from '@/lib/types/asset-modules';

// Real Estate Property Module - Property and facility management
export const realEstateModule: AssetModule = {
  id: 'real-estate-v1',
  name: 'Real Estate Property Management',
  description: 'Comprehensive property and facility management including leases, maintenance, utilities, and space allocation.',
  category: 'location',
  version: '1.2.0',
  author: 'WizeAssets Team',
  tags: ['property', 'real-estate', 'facilities', 'leases', 'maintenance'],
  isActive: true,
  isPublic: true,
  createdAt: new Date('2024-01-20'),
  updatedAt: new Date('2024-07-10'),

  fields: [
    {
      id: 'property-id',
      name: 'propertyId',
      type: 'text',
      label: 'Property ID',
      description: 'Unique property identifier',
      required: true,
      validation: {
        pattern: '^PROP-[0-9]{4}$',
        min: 9,
        max: 9
      },
      placeholder: 'PROP-1234'
    },
    {
      id: 'property-name',
      name: 'propertyName',
      type: 'text',
      label: 'Property Name',
      description: 'Name or description of the property',
      required: true,
      validation: {
        min: 2,
        max: 200
      },
      placeholder: 'e.g., Downtown Office Building, Warehouse A'
    },
    {
      id: 'property-type',
      name: 'propertyType',
      type: 'select',
      label: 'Property Type',
      description: 'Type of property',
      required: true,
      validation: {
        options: ['Office Building', 'Warehouse', 'Retail Space', 'Manufacturing Facility', 'Data Center', 'Parking Garage', 'Land', 'Residential']
      }
    },
    {
      id: 'address',
      name: 'address',
      type: 'text',
      label: 'Address',
      description: 'Full property address',
      required: true,
      validation: {
        min: 10,
        max: 300
      },
      placeholder: 'Street address, City, State, ZIP'
    },
    {
      id: 'total-area',
      name: 'totalArea',
      type: 'number',
      label: 'Total Area (sq ft)',
      description: 'Total square footage of the property',
      required: true,
      validation: {
        min: 100,
        max: 10000000
      },
      placeholder: '10000'
    },
    {
      id: 'usable-area',
      name: 'usableArea',
      type: 'number',
      label: 'Usable Area (sq ft)',
      description: 'Usable square footage',
      required: false,
      validation: {
        min: 50,
        max: 10000000
      },
      placeholder: '8500'
    },
    {
      id: 'ownership-type',
      name: 'ownershipType',
      type: 'select',
      label: 'Ownership Type',
      description: 'How the property is held',
      required: true,
      validation: {
        options: ['Owned', 'Leased', 'Subleased', 'Managed']
      }
    },
    {
      id: 'lease-start',
      name: 'leaseStart',
      type: 'date',
      label: 'Lease Start Date',
      description: 'Start date of lease (if applicable)',
      required: false,
      validation: {}
    },
    {
      id: 'lease-end',
      name: 'leaseEnd',
      type: 'date',
      label: 'Lease End Date',
      description: 'End date of lease (if applicable)',
      required: false,
      validation: {}
    },
    {
      id: 'monthly-rent',
      name: 'monthlyRent',
      type: 'number',
      label: 'Monthly Rent',
      description: 'Monthly rental cost in USD',
      required: false,
      validation: {
        min: 0,
        max: 1000000
      },
      placeholder: '0.00'
    },
    {
      id: 'security-deposit',
      name: 'securityDeposit',
      type: 'number',
      label: 'Security Deposit',
      description: 'Security deposit amount',
      required: false,
      validation: {
        min: 0,
        max: 10000000
      },
      placeholder: '0.00'
    },
    {
      id: 'property-manager',
      name: 'propertyManager',
      type: 'text',
      label: 'Property Manager',
      description: 'Person responsible for property management',
      required: false,
      validation: {
        max: 100
      },
      placeholder: 'Manager name or contact'
    },
    {
      id: 'utilities-included',
      name: 'utilitiesIncluded',
      type: 'multiselect',
      label: 'Utilities Included',
      description: 'Utilities included in rent',
      required: false,
      validation: {
        options: ['Electricity', 'Water', 'Gas', 'Internet', 'Heating', 'Cooling', 'Trash', 'Parking']
      }
    },
    {
      id: 'occupancy-rate',
      name: 'occupancyRate',
      type: 'number',
      label: 'Occupancy Rate (%)',
      description: 'Current occupancy percentage',
      required: false,
      validation: {
        min: 0,
        max: 100
      },
      placeholder: '85'
    },
    {
      id: 'last-inspection',
      name: 'lastInspection',
      type: 'date',
      label: 'Last Inspection',
      description: 'Date of last property inspection',
      required: false,
      validation: {}
    },
    {
      id: 'next-inspection',
      name: 'nextInspection',
      type: 'date',
      label: 'Next Inspection',
      description: 'Scheduled next inspection date',
      required: false,
      validation: {}
    },
    {
      id: 'property-value',
      name: 'propertyValue',
      type: 'number',
      label: 'Property Value',
      description: 'Current estimated property value',
      required: false,
      validation: {
        min: 0,
        max: 100000000
      },
      placeholder: '0.00'
    },
    {
      id: 'status',
      name: 'status',
      type: 'select',
      label: 'Status',
      description: 'Current property status',
      required: true,
      validation: {
        options: ['Active', 'Vacant', 'Under Renovation', 'For Sale', 'For Lease', 'Disposed']
      }
    }
  ],

  logic: {
    nodes: [
      {
        id: 'lease-expiry-check',
        type: 'condition',
        name: 'Lease Expiry Alert',
        description: 'Alert for upcoming lease expiration',
        position: { x: 100, y: 100 },
        config: {
          condition: 'leaseEnd <= today + 90 days',
          trueAction: 'trigger-lease-alert',
          falseAction: 'continue'
        }
      },
      {
        id: 'inspection-due',
        type: 'condition',
        name: 'Inspection Due Check',
        description: 'Check if inspection is due',
        position: { x: 300, y: 100 },
        config: {
          condition: 'nextInspection <= today + 7 days',
          trueAction: 'trigger-inspection-alert',
          falseAction: 'continue'
        }
      },
      {
        id: 'cost-per-sqft',
        type: 'calculation',
        name: 'Calculate Cost per Sq Ft',
        description: 'Calculate monthly cost per square foot',
        position: { x: 500, y: 100 },
        config: {
          formula: 'monthlyRent / usableArea',
          targetField: 'costPerSqFt'
        }
      }
    ],
    edges: [
      {
        id: 'edge-1',
        source: 'lease-expiry-check',
        target: 'inspection-due',
        label: 'Continue'
      },
      {
        id: 'edge-2',
        source: 'inspection-due',
        target: 'cost-per-sqft',
        label: 'Calculate'
      }
    ]
  },

  validation: {
    rules: [
      {
        id: 'usable-area-check',
        field: 'usableArea',
        type: 'custom',
        message: 'Usable area should not exceed total area',
        severity: 'error',
        condition: 'usableArea <= totalArea'
      },
      {
        id: 'lease-dates',
        field: 'leaseEnd',
        type: 'custom',
        message: 'Lease end date should be after start date',
        severity: 'error',
        condition: 'leaseEnd > leaseStart'
      }
    ]
  },

  rendering: {
    layout: 'grid',
    sections: [
      {
        id: 'basic-info',
        title: 'Property Information',
        fields: ['propertyId', 'propertyName', 'propertyType', 'address', 'totalArea', 'usableArea'],
        columns: 2
      },
      {
        id: 'ownership',
        title: 'Ownership & Lease Details',
        fields: ['ownershipType', 'leaseStart', 'leaseEnd', 'monthlyRent', 'securityDeposit'],
        columns: 2
      },
      {
        id: 'management',
        title: 'Management & Operations',
        fields: ['propertyManager', 'utilitiesIncluded', 'occupancyRate', 'status'],
        columns: 2
      },
      {
        id: 'maintenance',
        title: 'Inspections & Value',
        fields: ['lastInspection', 'nextInspection', 'propertyValue'],
        columns: 3
      }
    ]
  },

  permissions: {
    view: ['all'],
    edit: ['admin', 'facility-manager', 'property-manager'],
    delete: ['admin'],
    export: ['admin', 'facility-manager', 'finance']
  },

  integrations: {
    apis: ['property-valuation', 'lease-management', 'maintenance-scheduler'],
    webhooks: ['lease-expiry', 'inspection-due', 'occupancy-change'],
    exports: ['csv', 'pdf', 'property-report']
  },

  metadata: {
    documentation: 'Comprehensive property and facility management system',
    changeLog: [
      { version: '1.2.0', date: '2024-07-10', changes: 'Added occupancy tracking' },
      { version: '1.1.0', date: '2024-04-15', changes: 'Enhanced lease management' }
    ],
    usage: {
      totalAssets: 45,
      activeUsers: 6,
      lastUsed: new Date('2024-07-10')
    }
  }
};

// Manufacturing Equipment Module - Industrial equipment tracking
export const manufacturingEquipmentModule: AssetModule = {
  id: 'manufacturing-equipment-v1',
  name: 'Manufacturing Equipment',
  description: 'Track manufacturing equipment, production machinery, and industrial assets with maintenance schedules, performance metrics, and safety compliance.',
  category: 'hardware',
  version: '1.4.0',
  author: 'WizeAssets Team',
  tags: ['manufacturing', 'equipment', 'machinery', 'production', 'safety'],
  isActive: true,
  isPublic: true,
  createdAt: new Date('2024-02-15'),
  updatedAt: new Date('2024-07-10'),

  fields: [
    {
      id: 'equipment-id',
      name: 'equipmentId',
      type: 'text',
      label: 'Equipment ID',
      description: 'Unique equipment identifier',
      required: true,
      validation: {
        pattern: '^MFG-[0-9]{5}$',
        min: 9,
        max: 9
      },
      placeholder: 'MFG-12345'
    },
    {
      id: 'equipment-name',
      name: 'equipmentName',
      type: 'text',
      label: 'Equipment Name',
      description: 'Name or description of the equipment',
      required: true,
      validation: {
        min: 2,
        max: 150
      },
      placeholder: 'e.g., CNC Milling Machine, Assembly Line Robot'
    },
    {
      id: 'equipment-type',
      name: 'equipmentType',
      type: 'select',
      label: 'Equipment Type',
      description: 'Category of manufacturing equipment',
      required: true,
      validation: {
        options: ['CNC Machine', 'Assembly Line', 'Robot', 'Press', 'Conveyor', 'Packaging Machine', 'Quality Control', 'Material Handling', 'Other']
      }
    },
    {
      id: 'manufacturer',
      name: 'manufacturer',
      type: 'text',
      label: 'Manufacturer',
      description: 'Equipment manufacturer',
      required: true,
      validation: {
        min: 2,
        max: 100
      },
      placeholder: 'Manufacturer name'
    },
    {
      id: 'model-number',
      name: 'modelNumber',
      type: 'text',
      label: 'Model Number',
      description: 'Manufacturer model number',
      required: true,
      validation: {
        min: 1,
        max: 100
      },
      placeholder: 'Model number'
    },
    {
      id: 'serial-number',
      name: 'serialNumber',
      type: 'text',
      label: 'Serial Number',
      description: 'Equipment serial number',
      required: true,
      validation: {
        min: 3,
        max: 100
      },
      placeholder: 'Serial number'
    },
    {
      id: 'installation-date',
      name: 'installationDate',
      type: 'date',
      label: 'Installation Date',
      description: 'Date when equipment was installed',
      required: true,
      validation: {}
    },
    {
      id: 'production-line',
      name: 'productionLine',
      type: 'text',
      label: 'Production Line',
      description: 'Production line where equipment is located',
      required: false,
      validation: {
        max: 100
      },
      placeholder: 'Line A, Line B, etc.'
    },
    {
      id: 'location',
      name: 'location',
      type: 'text',
      label: 'Location',
      description: 'Physical location in facility',
      required: true,
      validation: {
        max: 200
      },
      placeholder: 'Building, Floor, Section'
    },
    {
      id: 'operating-hours',
      name: 'operatingHours',
      type: 'number',
      label: 'Operating Hours',
      description: 'Total operating hours',
      required: false,
      validation: {
        min: 0,
        max: 1000000
      },
      placeholder: '0'
    },
    {
      id: 'max-capacity',
      name: 'maxCapacity',
      type: 'number',
      label: 'Max Capacity',
      description: 'Maximum production capacity per hour',
      required: false,
      validation: {
        min: 0,
        max: 100000
      },
      placeholder: 'Units per hour'
    },
    {
      id: 'current-efficiency',
      name: 'currentEfficiency',
      type: 'number',
      label: 'Current Efficiency (%)',
      description: 'Current operational efficiency percentage',
      required: false,
      validation: {
        min: 0,
        max: 100
      },
      placeholder: '85'
    },
    {
      id: 'last-maintenance',
      name: 'lastMaintenance',
      type: 'date',
      label: 'Last Maintenance',
      description: 'Date of last maintenance service',
      required: false,
      validation: {}
    },
    {
      id: 'next-maintenance',
      name: 'nextMaintenance',
      type: 'date',
      label: 'Next Maintenance',
      description: 'Scheduled next maintenance date',
      required: false,
      validation: {}
    },
    {
      id: 'maintenance-interval',
      name: 'maintenanceInterval',
      type: 'number',
      label: 'Maintenance Interval (hours)',
      description: 'Operating hours between maintenance',
      required: false,
      validation: {
        min: 10,
        max: 10000
      },
      placeholder: '500'
    },
    {
      id: 'safety-certification',
      name: 'safetyCertification',
      type: 'text',
      label: 'Safety Certification',
      description: 'Safety certification number or type',
      required: false,
      validation: {
        max: 100
      },
      placeholder: 'OSHA, CE, UL certification'
    },
    {
      id: 'safety-expiry',
      name: 'safetyExpiry',
      type: 'date',
      label: 'Safety Certification Expiry',
      description: 'Safety certification expiration date',
      required: false,
      validation: {}
    },
    {
      id: 'status',
      name: 'status',
      type: 'select',
      label: 'Status',
      description: 'Current equipment status',
      required: true,
      validation: {
        options: ['Operational', 'Down for Maintenance', 'Out of Service', 'Under Repair', 'Decommissioned']
      }
    }
  ],

  logic: {
    nodes: [
      {
        id: 'maintenance-due-check',
        type: 'condition',
        name: 'Maintenance Due Check',
        description: 'Check if maintenance is due based on hours or date',
        position: { x: 100, y: 100 },
        config: {
          condition: 'operatingHours >= (lastMaintenanceHours + maintenanceInterval) OR nextMaintenance <= today',
          trueAction: 'trigger-maintenance-alert',
          falseAction: 'continue'
        }
      },
      {
        id: 'safety-expiry-check',
        type: 'condition',
        name: 'Safety Certification Check',
        description: 'Alert for safety certification renewal',
        position: { x: 300, y: 100 },
        config: {
          condition: 'safetyExpiry <= today + 60 days',
          trueAction: 'trigger-safety-alert',
          falseAction: 'continue'
        }
      },
      {
        id: 'efficiency-monitor',
        type: 'condition',
        name: 'Efficiency Monitor',
        description: 'Monitor equipment efficiency',
        position: { x: 500, y: 100 },
        config: {
          condition: 'currentEfficiency < 70',
          trueAction: 'trigger-efficiency-alert',
          falseAction: 'continue'
        }
      }
    ],
    edges: [
      {
        id: 'edge-1',
        source: 'maintenance-due-check',
        target: 'safety-expiry-check',
        label: 'Continue'
      },
      {
        id: 'edge-2',
        source: 'safety-expiry-check',
        target: 'efficiency-monitor',
        label: 'Monitor'
      }
    ]
  },

  validation: {
    rules: [
      {
        id: 'serial-unique',
        field: 'serialNumber',
        type: 'unique',
        message: 'Serial number must be unique across all equipment',
        severity: 'error'
      },
      {
        id: 'efficiency-range',
        field: 'currentEfficiency',
        type: 'custom',
        message: 'Efficiency should be between 0 and 100%',
        severity: 'error',
        condition: 'currentEfficiency >= 0 AND currentEfficiency <= 100'
      }
    ]
  },

  rendering: {
    layout: 'tabs',
    sections: [
      {
        id: 'equipment-info',
        title: 'Equipment Information',
        fields: ['equipmentId', 'equipmentName', 'equipmentType', 'manufacturer', 'modelNumber', 'serialNumber'],
        columns: 2
      },
      {
        id: 'location-setup',
        title: 'Location & Setup',
        fields: ['installationDate', 'productionLine', 'location', 'status'],
        columns: 2
      },
      {
        id: 'performance',
        title: 'Performance Metrics',
        fields: ['operatingHours', 'maxCapacity', 'currentEfficiency'],
        columns: 3
      },
      {
        id: 'maintenance',
        title: 'Maintenance Schedule',
        fields: ['lastMaintenance', 'nextMaintenance', 'maintenanceInterval'],
        columns: 3
      },
      {
        id: 'safety',
        title: 'Safety & Compliance',
        fields: ['safetyCertification', 'safetyExpiry'],
        columns: 2
      }
    ]
  },

  permissions: {
    view: ['all'],
    edit: ['admin', 'production-manager', 'maintenance-supervisor'],
    delete: ['admin'],
    export: ['admin', 'production-manager', 'maintenance-supervisor', 'safety-officer']
  },

  integrations: {
    apis: ['production-monitoring', 'maintenance-scheduler', 'safety-compliance'],
    webhooks: ['maintenance-due', 'safety-expiry', 'efficiency-alert'],
    exports: ['csv', 'pdf', 'production-report']
  },

  metadata: {
    documentation: 'Manufacturing equipment tracking with performance and safety monitoring',
    changeLog: [
      { version: '1.4.0', date: '2024-07-10', changes: 'Added efficiency monitoring' },
      { version: '1.3.0', date: '2024-05-20', changes: 'Enhanced safety compliance tracking' }
    ],
    usage: {
      totalAssets: 89,
      activeUsers: 12,
      lastUsed: new Date('2024-07-10')
    }
  }
};

// Export additional modules
export const additionalSeedModules: AssetModule[] = [
  realEstateModule,
  manufacturingEquipmentModule
];
