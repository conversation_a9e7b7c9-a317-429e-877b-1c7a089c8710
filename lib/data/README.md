# Asset Module Seed Data

This directory contains comprehensive seed data for the WizeAssets ERP Asset Module system, providing production-ready examples that showcase the full capabilities of the Asset Module Editor.

## 📦 **Included Modules**

### 1. **IT Equipment Management** (`it-equipment-v2`)
- **Category**: Hardware
- **Purpose**: Comprehensive tracking of computers, servers, and IT hardware
- **Features**: 
  - Warranty tracking with automated alerts
  - Maintenance scheduling
  - Asset depreciation calculations
  - User assignment and location tracking
- **Fields**: 15 comprehensive fields including asset tags, specifications, warranty info
- **Sample Assets**: 2 realistic examples (laptop and server)

### 2. **Software License Management** (`software-license-v1`)
- **Category**: Software
- **Purpose**: Track software licenses, compliance, and renewals
- **Features**:
  - License compliance monitoring
  - Renewal alerts and cost tracking
  - Usage vs. available license tracking
  - Vendor and support level management
- **Fields**: 14 fields covering licensing, compliance, and financial aspects
- **Sample Assets**: 2 examples (Office 365 and Adobe Creative Suite)

### 3. **Vehicle Fleet Management** (`vehicle-fleet-v1`)
- **Category**: Vehicle
- **Purpose**: Complete vehicle fleet tracking and maintenance
- **Features**:
  - Insurance and registration tracking
  - Maintenance scheduling based on mileage/time
  - Driver assignment and department allocation
  - Compliance monitoring
- **Fields**: 18 comprehensive fields for vehicle management
- **Sample Assets**: 1 example (Ford F-150 delivery truck)

### 4. **Real Estate Property Management** (`real-estate-v1`)
- **Category**: Location
- **Purpose**: Property and facility management
- **Features**:
  - Lease management and renewal tracking
  - Occupancy rate monitoring
  - Utility and cost tracking
  - Inspection scheduling
- **Fields**: 18 fields covering property details, leases, and management
- **Sample Assets**: 1 example (downtown office building)

### 5. **Manufacturing Equipment** (`manufacturing-equipment-v1`)
- **Category**: Hardware
- **Purpose**: Industrial equipment and machinery tracking
- **Features**:
  - Performance and efficiency monitoring
  - Safety certification tracking
  - Maintenance scheduling based on operating hours
  - Production line integration
- **Fields**: 18 fields for equipment management and safety
- **Sample Assets**: 1 example (CNC milling machine)

## 🏗️ **Module Architecture**

Each module demonstrates the complete Asset Module system capabilities:

### **Field Types Showcased**
- `text` - Basic text input with validation
- `number` - Numeric fields with min/max validation
- `date` - Date fields for scheduling and tracking
- `select` - Dropdown selections with predefined options
- `multiselect` - Multiple selection fields
- `boolean` - Yes/no checkboxes

### **Validation Rules**
- **Pattern Validation**: Asset tags, VINs, license plates
- **Range Validation**: Numbers, dates, percentages
- **Unique Constraints**: Serial numbers, license plates
- **Custom Logic**: Cross-field validation rules
- **Required Fields**: Critical data enforcement

### **Business Logic Workflows**
- **Conditional Logic**: Status-based workflows
- **Automated Calculations**: Depreciation, cost per unit
- **Alert Triggers**: Warranty expiry, maintenance due
- **Data Relationships**: Cross-field dependencies

### **Rendering Layouts**
- **Grid Layout**: Organized field sections
- **Tab Layout**: Grouped functionality
- **Accordion Layout**: Expandable sections
- **Responsive Design**: Multi-column layouts

## 📊 **Sample Data Statistics**

```
Total Modules: 5
Total Sample Assets: 7
Categories Covered: 4 (Hardware, Software, Vehicle, Location)
Total Fields: 83 across all modules
Validation Rules: 15+ comprehensive rules
Logic Nodes: 15+ automated workflows
```

## 🚀 **Usage Instructions**

### **Development Setup**
```bash
# Initialize seed data in your application
import { initializeSeedData } from '@/lib/data/seed-data-initializer';

const seedData = await initializeSeedData();
console.log(`Loaded ${seedData.modules.length} modules`);
```

### **Database Seeding**
```bash
# Run Prisma seeding
npx prisma db seed

# Or run module-specific seeding
npx ts-node prisma/seed-modules.ts
```

### **Accessing Modules**
```typescript
import { getModuleById, getModulesByCategory } from '@/lib/data/seed-data-initializer';

// Get specific module
const itModule = getModuleById('it-equipment-v2');

// Get modules by category
const hardwareModules = getModulesByCategory('hardware');

// Get sample assets for a module
const sampleAssets = getSampleAssetsForModule('it-equipment-v2');
```

## 🎯 **Use Cases Demonstrated**

### **Asset Lifecycle Management**
- Purchase tracking and financial management
- Warranty and maintenance scheduling
- Depreciation and value tracking
- Disposal and retirement workflows

### **Compliance Monitoring**
- License compliance and usage tracking
- Safety certification management
- Insurance and registration tracking
- Audit trail and documentation

### **Operational Efficiency**
- Automated alert systems
- Performance monitoring
- Resource allocation tracking
- Cost optimization insights

### **Integration Capabilities**
- API endpoints for external systems
- Webhook notifications for events
- Export capabilities for reporting
- Permission-based access control

## 🔧 **Customization Guide**

### **Adding New Modules**
1. Create module definition in `seed-modules.ts`
2. Add sample asset instances
3. Update the module statistics
4. Test with the Asset Module Editor

### **Modifying Existing Modules**
1. Update field definitions as needed
2. Adjust validation rules
3. Modify business logic workflows
4. Update sample data accordingly

### **Field Configuration Examples**
```typescript
// Text field with pattern validation
{
  id: 'asset-tag',
  name: 'assetTag',
  type: 'text',
  label: 'Asset Tag',
  required: true,
  validation: {
    pattern: '^IT-[0-9]{6}$',
    min: 9,
    max: 9
  }
}

// Select field with predefined options
{
  id: 'status',
  name: 'status',
  type: 'select',
  label: 'Status',
  required: true,
  validation: {
    options: ['Active', 'Inactive', 'Retired']
  }
}
```

## 📈 **Performance Considerations**

- **Lazy Loading**: Large modules load fields on demand
- **Validation Caching**: Rules cached for performance
- **Optimized Queries**: Efficient database access patterns
- **Memory Management**: Large datasets handled efficiently

## 🔒 **Security Features**

- **Permission-based Access**: Role-based field access
- **Data Encryption**: Sensitive fields (license keys) encrypted
- **Audit Logging**: All changes tracked
- **Input Validation**: Comprehensive sanitization

## 📝 **Documentation Standards**

Each module includes:
- Comprehensive field descriptions
- Business logic explanations
- Usage examples and best practices
- Change logs and version history
- Integration guidelines

This seed data provides a solid foundation for developing and testing the Asset Module system, demonstrating real-world use cases and best practices for enterprise asset management.
