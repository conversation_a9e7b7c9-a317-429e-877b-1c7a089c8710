// Seed Data Initializer for Asset Modules
// Comprehensive initialization script for development and demo environments

import { AssetModule } from '@/lib/types/asset-modules';
import { seedModules } from './seed-modules';
import { additionalSeedModules } from './additional-seed-modules';

// Combine all seed modules
export const allSeedModules: AssetModule[] = [
  ...seedModules,
  ...additionalSeedModules
];

// Sample asset instances for each module type
export const sampleAssetInstances = {
  'it-equipment-v2': [
    {
      id: 'asset-1',
      moduleId: 'it-equipment-v2',
      data: {
        assetTag: 'IT-123456',
        deviceType: 'Laptop',
        manufacturer: 'Dell',
        model: 'Latitude 7420',
        serialNumber: 'DL7420-001234',
        specifications: 'Intel i7-1185G7, 16GB RAM, 512GB SSD',
        operatingSystem: 'Windows 11',
        purchaseDate: '2024-01-15',
        purchasePrice: 1299.99,
        warrantyExpiry: '2027-01-15',
        assignedUser: '<PERSON>',
        location: 'Building A, Floor 3, Desk 15',
        status: 'Active',
        lastMaintenance: '2024-06-01',
        nextMaintenance: '2024-12-01'
      },
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-07-10')
    },
    {
      id: 'asset-2',
      moduleId: 'it-equipment-v2',
      data: {
        assetTag: 'IT-123457',
        deviceType: 'Server',
        manufacturer: 'HP',
        model: 'ProLiant DL380 Gen10',
        serialNumber: 'HP380-567890',
        specifications: 'Intel Xeon Silver 4214, 64GB RAM, 2TB SSD RAID',
        operatingSystem: 'Ubuntu 22.04',
        purchaseDate: '2023-08-20',
        purchasePrice: 4599.99,
        warrantyExpiry: '2026-08-20',
        assignedUser: 'IT Infrastructure Team',
        location: 'Data Center, Rack 15',
        status: 'Active',
        lastMaintenance: '2024-05-15',
        nextMaintenance: '2024-11-15'
      },
      createdAt: new Date('2023-08-20'),
      updatedAt: new Date('2024-07-10')
    }
  ],

  'software-license-v1': [
    {
      id: 'license-1',
      moduleId: 'software-license-v1',
      data: {
        softwareName: 'Microsoft Office 365 Business Premium',
        vendor: 'Microsoft',
        licenseType: 'Subscription',
        licenseKey: 'XXXXX-XXXXX-XXXXX-XXXXX-XXXXX',
        totalLicenses: 50,
        usedLicenses: 47,
        purchaseDate: '2024-01-01',
        expiryDate: '2024-12-31',
        annualCost: 10500.00,
        costPerLicense: 210.00,
        renewalDate: '2024-12-01',
        complianceStatus: 'Compliant',
        installationCount: 47,
        supportLevel: 'Standard'
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-07-10')
    },
    {
      id: 'license-2',
      moduleId: 'software-license-v1',
      data: {
        softwareName: 'Adobe Creative Suite',
        vendor: 'Adobe',
        licenseType: 'Subscription',
        licenseKey: 'ADOBE-XXXXX-XXXXX-XXXXX',
        totalLicenses: 10,
        usedLicenses: 12,
        purchaseDate: '2023-06-15',
        expiryDate: '2024-06-15',
        annualCost: 6000.00,
        costPerLicense: 600.00,
        renewalDate: '2024-05-15',
        complianceStatus: 'Over-licensed',
        installationCount: 12,
        supportLevel: 'Premium'
      },
      createdAt: new Date('2023-06-15'),
      updatedAt: new Date('2024-07-10')
    }
  ],

  'vehicle-fleet-v1': [
    {
      id: 'vehicle-1',
      moduleId: 'vehicle-fleet-v1',
      data: {
        vehicleId: 'VH-1001',
        licensePlate: 'ABC-1234',
        make: 'Ford',
        model: 'F-150',
        year: 2023,
        vin: '1FTFW1ET5NFC12345',
        vehicleType: 'Truck',
        fuelType: 'Gasoline',
        currentMileage: 15420,
        assignedDriver: 'Mike Johnson',
        department: 'Delivery',
        insurancePolicy: 'POL-2024-001',
        insuranceExpiry: '2025-03-15',
        registrationExpiry: '2025-01-31',
        lastService: '2024-05-20',
        nextService: '2024-08-20',
        serviceInterval: 5000,
        status: 'Active'
      },
      createdAt: new Date('2023-02-15'),
      updatedAt: new Date('2024-07-10')
    }
  ],

  'real-estate-v1': [
    {
      id: 'property-1',
      moduleId: 'real-estate-v1',
      data: {
        propertyId: 'PROP-1001',
        propertyName: 'Downtown Office Building',
        propertyType: 'Office Building',
        address: '123 Business Ave, Downtown, NY 10001',
        totalArea: 25000,
        usableArea: 22000,
        ownershipType: 'Leased',
        leaseStart: '2023-01-01',
        leaseEnd: '2028-12-31',
        monthlyRent: 45000.00,
        securityDeposit: 135000.00,
        propertyManager: 'Sarah Wilson',
        utilitiesIncluded: ['Electricity', 'Water', 'Heating', 'Cooling'],
        occupancyRate: 92,
        lastInspection: '2024-03-15',
        nextInspection: '2024-09-15',
        propertyValue: 8500000.00,
        status: 'Active'
      },
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2024-07-10')
    }
  ],

  'manufacturing-equipment-v1': [
    {
      id: 'equipment-1',
      moduleId: 'manufacturing-equipment-v1',
      data: {
        equipmentId: 'MFG-10001',
        equipmentName: 'CNC Milling Machine #1',
        equipmentType: 'CNC Machine',
        manufacturer: 'Haas Automation',
        modelNumber: 'VF-2SS',
        serialNumber: 'HAAS-VF2SS-2023-001',
        installationDate: '2023-03-15',
        productionLine: 'Line A',
        location: 'Building 1, Floor 1, Section A',
        operatingHours: 3240,
        maxCapacity: 25,
        currentEfficiency: 87,
        lastMaintenance: '2024-06-01',
        nextMaintenance: '2024-09-01',
        maintenanceInterval: 500,
        safetyCertification: 'OSHA-2023-CNC-001',
        safetyExpiry: '2025-03-15',
        status: 'Operational'
      },
      createdAt: new Date('2023-03-15'),
      updatedAt: new Date('2024-07-10')
    }
  ]
};

// Module statistics for dashboard
export const moduleStatistics = {
  totalModules: allSeedModules.length,
  modulesByCategory: {
    hardware: allSeedModules.filter(m => m.category === 'hardware').length,
    software: allSeedModules.filter(m => m.category === 'software').length,
    vehicle: allSeedModules.filter(m => m.category === 'vehicle').length,
    location: allSeedModules.filter(m => m.category === 'location').length
  },
  totalAssets: Object.values(sampleAssetInstances).reduce((sum, instances) => sum + instances.length, 0),
  activeModules: allSeedModules.filter(m => m.isActive).length,
  publicModules: allSeedModules.filter(m => m.isPublic).length
};

// Seed data initialization function
export async function initializeSeedData() {
  console.log('🌱 Initializing Asset Module seed data...');
  
  try {
    // Log module information
    console.log(`📦 Loading ${allSeedModules.length} asset modules:`);
    allSeedModules.forEach(module => {
      console.log(`  - ${module.name} (${module.category}) v${module.version}`);
    });

    // Log sample assets
    const totalSampleAssets = Object.values(sampleAssetInstances).reduce((sum, instances) => sum + instances.length, 0);
    console.log(`📋 Loading ${totalSampleAssets} sample asset instances`);

    // Log statistics
    console.log('📊 Module Statistics:');
    console.log(`  - Total Modules: ${moduleStatistics.totalModules}`);
    console.log(`  - Active Modules: ${moduleStatistics.activeModules}`);
    console.log(`  - Public Modules: ${moduleStatistics.publicModules}`);
    console.log(`  - Total Sample Assets: ${moduleStatistics.totalAssets}`);
    console.log('  - By Category:');
    Object.entries(moduleStatistics.modulesByCategory).forEach(([category, count]) => {
      console.log(`    - ${category}: ${count} modules`);
    });

    console.log('✅ Seed data initialization complete!');
    
    return {
      modules: allSeedModules,
      sampleAssets: sampleAssetInstances,
      statistics: moduleStatistics
    };
  } catch (error) {
    console.error('❌ Error initializing seed data:', error);
    throw error;
  }
}

// Helper functions for development
export function getModuleById(id: string): AssetModule | undefined {
  return allSeedModules.find(module => module.id === id);
}

export function getModulesByCategory(category: string): AssetModule[] {
  return allSeedModules.filter(module => module.category === category);
}

export function getSampleAssetsForModule(moduleId: string) {
  return sampleAssetInstances[moduleId as keyof typeof sampleAssetInstances] || [];
}

export function getModuleUsageStats() {
  return allSeedModules.map(module => ({
    id: module.id,
    name: module.name,
    category: module.category,
    version: module.version,
    totalAssets: module.metadata?.usage?.totalAssets || 0,
    activeUsers: module.metadata?.usage?.activeUsers || 0,
    lastUsed: module.metadata?.usage?.lastUsed || module.updatedAt
  }));
}

// Export for easy access
export default {
  modules: allSeedModules,
  sampleAssets: sampleAssetInstances,
  statistics: moduleStatistics,
  initialize: initializeSeedData,
  getModuleById,
  getModulesByCategory,
  getSampleAssetsForModule,
  getModuleUsageStats
};
