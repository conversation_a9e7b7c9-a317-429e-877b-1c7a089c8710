// AI Configuration for Asset Module Editor
// Centralized configuration for AI features

export const AI_CONFIG = {
  // Feature flags
  enabled: process.env.AI_ENABLED === 'true',
  
  // Model configuration
  models: {
    default: process.env.AI_DEFAULT_MODEL || 'gpt-4o',
    fallback: process.env.AI_FALLBACK_MODEL || 'gpt-4o-mini',
    reasoning: 'claude-3-5-sonnet-20241022'
  },
  
  // Rate limiting
  rateLimits: {
    requestsPerMinute: parseInt(process.env.AI_RATE_LIMIT_REQUESTS_PER_MINUTE || '60'),
    tokensPerMinute: parseInt(process.env.AI_RATE_LIMIT_TOKENS_PER_MINUTE || '100000')
  },
  
  // Timeouts (in seconds)
  timeouts: {
    chat: 60,
    analysis: 45,
    suggestions: 30,
    generation: 60
  },
  
  // Feature-specific settings
  features: {
    fieldSuggestions: {
      maxSuggestions: 8,
      minConfidence: 0.3,
      autoSelectThreshold: 0.8
    },
    moduleAnalysis: {
      includeComparison: true,
      detailedRecommendations: true,
      benchmarkScoring: true
    },
    chat: {
      maxMessages: 50,
      contextWindow: 10,
      enableTools: true
    }
  },
  
  // Logging
  logging: {
    level: process.env.AI_LOG_LEVEL || 'info',
    logRequests: process.env.AI_LOG_REQUESTS === 'true'
  }
} as const;

// Validation function
export function validateAIConfig() {
  const errors: string[] = [];
  
  if (!process.env.OPENAI_API_KEY) {
    errors.push('OPENAI_API_KEY is required for AI features');
  }
  
  if (AI_CONFIG.rateLimits.requestsPerMinute <= 0) {
    errors.push('AI_RATE_LIMIT_REQUESTS_PER_MINUTE must be positive');
  }
  
  if (AI_CONFIG.rateLimits.tokensPerMinute <= 0) {
    errors.push('AI_RATE_LIMIT_TOKENS_PER_MINUTE must be positive');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Helper functions
export function isAIEnabled(): boolean {
  return AI_CONFIG.enabled && !!process.env.OPENAI_API_KEY;
}

export function getAIModel(type: 'default' | 'fallback' | 'reasoning' = 'default'): string {
  return AI_CONFIG.models[type];
}

export function getTimeout(feature: keyof typeof AI_CONFIG.timeouts): number {
  return AI_CONFIG.timeouts[feature];
}

export function shouldLogRequests(): boolean {
  return AI_CONFIG.logging.logRequests;
}
