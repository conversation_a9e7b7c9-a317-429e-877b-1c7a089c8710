// AI Tools for Asset Module Editor
// Production-ready AI tools using Vercel AI SDK

import { tool } from 'ai';
import { z } from 'zod';
import { AssetModule, ModuleField } from '@/lib/types/asset-modules';
import { ModuleAIAgentFactory } from '../agents/module-ai-agents';

// Tool Schemas
const ModuleDescriptionSchema = z.object({
  description: z.string().describe('Description of the module functionality'),
  category: z.string().describe('Module category (location, software, hardware, etc.)'),
  existingFields: z.array(z.string()).optional().describe('Names of existing fields')
});

const ModuleAnalysisSchema = z.object({
  moduleData: z.string().describe('JSON string of the module to analyze')
});

const LogicRequirementsSchema = z.object({
  requirements: z.string().describe('Business logic requirements description'),
  fields: z.array(z.string()).describe('Available field names'),
  context: z.string().optional().describe('Additional context or constraints')
});

const ValidationSchema = z.object({
  fields: z.string().describe('JSON string of fields to validate'),
  context: z.string().optional().describe('Validation context')
});

// AI Tools for Module Editor
export const moduleAITools = {
  // Field Suggestion Tool
  suggestFields: tool({
    description: 'Suggests appropriate fields for an asset module based on description and category. Provides intelligent field recommendations with proper data types and validation rules.',
    parameters: ModuleDescriptionSchema,
    execute: async ({ description, category, existingFields = [] }) => {
      const fieldAgent = ModuleAIAgentFactory.getFieldAgent();
      
      // Convert existing field names to ModuleField objects (simplified)
      const existingFieldObjects: ModuleField[] = existingFields.map(name => ({
        id: name.toLowerCase().replace(/\s+/g, '-'),
        name,
        type: 'text',
        label: name,
        required: false,
        validation: {}
      }));

      const result = await fieldAgent.suggestFields(description, category, existingFieldObjects);
      
      if (result.success) {
        return {
          success: true,
          suggestions: result.data.fields,
          confidence: result.data.confidence,
          explanation: result.data.explanation,
          timestamp: result.timestamp
        };
      } else {
        return {
          success: false,
          error: result.error,
          timestamp: result.timestamp
        };
      }
    }
  }),

  // Module Analysis Tool
  analyzeModule: tool({
    description: 'Performs comprehensive analysis of an asset module including completeness, quality assessment, and improvement suggestions.',
    parameters: ModuleAnalysisSchema,
    execute: async ({ moduleData }) => {
      try {
        const module: AssetModule = JSON.parse(moduleData);
        const analysisAgent = ModuleAIAgentFactory.getAnalysisAgent();
        
        const result = await analysisAgent.analyzeModule(module);
        
        if (result.success) {
          return {
            success: true,
            analysis: result.analysis,
            timestamp: result.timestamp
          };
        } else {
          return {
            success: false,
            error: result.error,
            timestamp: result.timestamp
          };
        }
      } catch (error) {
        return {
          success: false,
          error: 'Invalid module data format',
          timestamp: new Date().toISOString()
        };
      }
    }
  }),

  // Logic Generation Tool
  generateLogic: tool({
    description: 'Generates business logic flows for asset modules based on requirements. Creates nodes and connections for workflow automation.',
    parameters: LogicRequirementsSchema,
    execute: async ({ requirements, fields, context = '' }) => {
      const logicAgent = ModuleAIAgentFactory.getLogicAgent();
      
      // Convert field names to ModuleField objects (simplified)
      const fieldObjects: ModuleField[] = fields.map(name => ({
        id: name.toLowerCase().replace(/\s+/g, '-'),
        name,
        type: 'text',
        label: name,
        required: false,
        validation: {}
      }));

      const fullRequirements = context ? `${requirements}\n\nAdditional Context: ${context}` : requirements;
      const result = await logicAgent.generateLogic(fullRequirements, fieldObjects);
      
      if (result.success) {
        return {
          success: true,
          logic: result.logic,
          timestamp: result.timestamp
        };
      } else {
        return {
          success: false,
          error: result.error,
          timestamp: result.timestamp
        };
      }
    }
  }),

  // Field Validation Tool
  validateFields: tool({
    description: 'Validates field configurations and provides improvement suggestions for data integrity and user experience.',
    parameters: ValidationSchema,
    execute: async ({ fields, context = '' }) => {
      try {
        const fieldObjects: ModuleField[] = JSON.parse(fields);
        const fieldAgent = ModuleAIAgentFactory.getFieldAgent();
        
        const result = await fieldAgent.validateFields(fieldObjects);
        
        if (result.success) {
          return {
            success: true,
            validation: result.validation,
            context: context,
            timestamp: result.timestamp
          };
        } else {
          return {
            success: false,
            error: result.error,
            timestamp: result.timestamp
          };
        }
      } catch (error) {
        return {
          success: false,
          error: 'Invalid fields data format',
          timestamp: new Date().toISOString()
        };
      }
    }
  }),

  // Module Optimization Tool
  optimizeModule: tool({
    description: 'Provides optimization recommendations for asset modules including performance improvements and best practices.',
    parameters: ModuleAnalysisSchema,
    execute: async ({ moduleData }) => {
      try {
        const module: AssetModule = JSON.parse(moduleData);
        const analysisAgent = ModuleAIAgentFactory.getAnalysisAgent();
        
        // Analyze the module first
        const analysisResult = await analysisAgent.analyzeModule(module);
        
        if (analysisResult.success) {
          // Generate optimization recommendations based on analysis
          const optimizations = analysisResult.analysis.analysis.suggestions.map(suggestion => ({
            type: suggestion.type,
            title: suggestion.title,
            description: suggestion.description,
            impact: suggestion.impact,
            priority: suggestion.impact === 'high' ? 1 : suggestion.impact === 'medium' ? 2 : 3
          }));

          return {
            success: true,
            optimizations: optimizations.sort((a, b) => a.priority - b.priority),
            score: analysisResult.analysis.analysis.score,
            recommendations: analysisResult.analysis.recommendations,
            timestamp: analysisResult.timestamp
          };
        } else {
          return {
            success: false,
            error: analysisResult.error,
            timestamp: analysisResult.timestamp
          };
        }
      } catch (error) {
        return {
          success: false,
          error: 'Invalid module data format',
          timestamp: new Date().toISOString()
        };
      }
    }
  }),

  // Smart Documentation Tool
  generateDocumentation: tool({
    description: 'Generates comprehensive documentation for asset modules including usage guides and technical specifications.',
    parameters: ModuleAnalysisSchema,
    execute: async ({ moduleData }) => {
      try {
        const module: AssetModule = JSON.parse(moduleData);
        
        // Generate documentation using AI
        const documentation = {
          overview: `# ${module.name} Module Documentation\n\n${module.description}`,
          fields: module.fields.map(field => ({
            name: field.name,
            type: field.type,
            description: field.label,
            required: field.required,
            validation: field.validation
          })),
          usage: `## Usage Instructions\n\nThis module is designed for ${module.category} asset management.`,
          examples: `## Examples\n\nCommon use cases and configuration examples.`,
          troubleshooting: `## Troubleshooting\n\nCommon issues and solutions.`
        };

        return {
          success: true,
          documentation,
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        return {
          success: false,
          error: 'Invalid module data format',
          timestamp: new Date().toISOString()
        };
      }
    }
  })
};

// Tool Categories for UI Organization
export const AI_TOOL_CATEGORIES = {
  design: {
    name: 'Design & Structure',
    tools: ['suggestFields', 'validateFields'],
    icon: 'Layers',
    description: 'AI-powered field design and validation'
  },
  analysis: {
    name: 'Analysis & Insights',
    tools: ['analyzeModule', 'optimizeModule'],
    icon: 'BarChart3',
    description: 'Comprehensive module analysis and optimization'
  },
  automation: {
    name: 'Logic & Automation',
    tools: ['generateLogic'],
    icon: 'Workflow',
    description: 'Intelligent business logic generation'
  },
  documentation: {
    name: 'Documentation',
    tools: ['generateDocumentation'],
    icon: 'FileText',
    description: 'Automated documentation generation'
  }
} as const;

// Helper function to get tools by category
export function getToolsByCategory(category: keyof typeof AI_TOOL_CATEGORIES) {
  const categoryConfig = AI_TOOL_CATEGORIES[category];
  return categoryConfig.tools.map(toolName => ({
    name: toolName,
    tool: moduleAITools[toolName as keyof typeof moduleAITools]
  }));
}

// Export all tools as a single object for easy integration
export const allModuleAITools = moduleAITools;
