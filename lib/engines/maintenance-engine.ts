import { MaintenanceFrequency, MaintenanceTrigger } from "@/lib/modules/asset-types/types";
import { MaintenanceSchedule, MaintenanceTypeEnum, MaintenancePriorityEnum } from "@/lib/schemas/maintenance";
import prisma from "@/lib/prisma";

// Type conversion helper to handle database string to enum conversion
function convertDatabaseScheduleToMaintenanceSchedule(dbSchedule: any): MaintenanceSchedule {
  try {
    return {
      id: dbSchedule.id,
      assetTypeId: dbSchedule.assetTypeId,
      name: dbSchedule.name,
      description: dbSchedule.description,
      type: MaintenanceTypeEnum.parse(dbSchedule.type), // Convert string to enum
      frequency: typeof dbSchedule.frequency === 'string'
        ? JSON.parse(dbSchedule.frequency)
        : dbSchedule.frequency,
      priority: MaintenancePriorityEnum.parse(dbSchedule.priority), // Convert string to enum
      estimatedDuration: dbSchedule.estimatedDuration,
      estimatedCost: dbSchedule.estimatedCost,
      requiredSkills: Array.isArray(dbSchedule.requiredSkills)
        ? dbSchedule.requiredSkills
        : (typeof dbSchedule.requiredSkills === 'string' && dbSchedule.requiredSkills
          ? JSON.parse(dbSchedule.requiredSkills)
          : []),
      requiredParts: typeof dbSchedule.requiredParts === 'string' && dbSchedule.requiredParts
        ? JSON.parse(dbSchedule.requiredParts)
        : (dbSchedule.requiredParts || []),
      instructions: dbSchedule.instructions,
      checklistItems: typeof dbSchedule.checklistItems === 'string' && dbSchedule.checklistItems
        ? JSON.parse(dbSchedule.checklistItems)
        : (dbSchedule.checklistItems || []),
      triggers: typeof dbSchedule.triggers === 'string' && dbSchedule.triggers
        ? JSON.parse(dbSchedule.triggers)
        : (dbSchedule.triggers || []),
      isActive: dbSchedule.isActive,
      createdAt: dbSchedule.createdAt,
      updatedAt: dbSchedule.updatedAt,
    };
  } catch (error) {
    console.error('Error converting database schedule to MaintenanceSchedule:', error);
    console.error('Database schedule data:', dbSchedule);
    throw new Error(`Failed to convert database schedule: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export interface MaintenanceTaskGenerationInput {
  assetId: string;
  scheduleId: string;
  schedule: MaintenanceSchedule;
  lastMaintenanceDate?: Date;
  currentDate?: Date;
}

export interface MaintenanceTaskGenerationResult {
  tasksGenerated: number;
  nextScheduledDate?: Date;
  errors: string[];
  warnings: string[];
}

export interface MaintenanceMetrics {
  totalTasks: number;
  completedTasks: number;
  overdueTasks: number;
  upcomingTasks: number;
  averageCompletionTime: number;
  totalCost: number;
  tasksByType: Record<string, number>;
  tasksByPriority: Record<string, number>;
}

export class MaintenanceEngine {
  /**
   * Generate maintenance tasks based on schedules
   */
  static async generateMaintenanceTasks(
    input: MaintenanceTaskGenerationInput
  ): Promise<MaintenanceTaskGenerationResult> {
    const { assetId, scheduleId, schedule, lastMaintenanceDate, currentDate = new Date() } = input;
    
    const result: MaintenanceTaskGenerationResult = {
      tasksGenerated: 0,
      errors: [],
      warnings: [],
    };
    
    try {
      if (!schedule.isActive) {
        result.warnings.push("Schedule is not active");
        return result;
      }
      
      // Calculate next maintenance dates
      const nextDates = this.calculateNextMaintenanceDates(
        schedule.frequency,
        lastMaintenanceDate || currentDate,
        currentDate
      );
      
      // Generate tasks for each date
      for (const date of nextDates) {
        // Check if task already exists
        const existingTask = await prisma.maintenanceTask.findFirst({
          where: {
            assetId,
            scheduleId,
            scheduledDate: date,
          },
        });
        
        if (existingTask) {
          result.warnings.push(`Task already exists for ${date.toISOString()}`);
          continue;
        }
        
        // Create maintenance task
        await prisma.maintenanceTask.create({
          data: {
            assetId,
            scheduleId,
            title: schedule.name,
            description: schedule.description,
            type: schedule.type,
            priority: schedule.priority,
            scheduledDate: date,
            dueDate: new Date(date.getTime() + (schedule.estimatedDuration * 60 * 1000)), // Add estimated duration
            estimatedDuration: schedule.estimatedDuration,
            estimatedCost: schedule.estimatedCost,
            instructions: schedule.instructions,
            checklistItems: JSON.stringify(schedule.checklistItems),
          },
        });
        
        result.tasksGenerated++;
      }
      
      // Set next scheduled date
      if (nextDates.length > 0) {
        result.nextScheduledDate = nextDates[nextDates.length - 1];
      }
      
    } catch (error) {
      result.errors.push(`Failed to generate maintenance tasks: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    
    return result;
  }
  
  /**
   * Calculate next maintenance dates based on frequency
   */
  private static calculateNextMaintenanceDates(
    frequency: MaintenanceFrequency,
    lastDate: Date,
    currentDate: Date,
    lookAheadMonths: number = 6
  ): Date[] {
    const dates: Date[] = [];
    const endDate = new Date(currentDate);
    endDate.setMonth(endDate.getMonth() + lookAheadMonths);
    
    let nextDate = new Date(lastDate);
    
    switch (frequency.type) {
      case "days":
        while (nextDate <= endDate) {
          nextDate = new Date(nextDate);
          nextDate.setDate(nextDate.getDate() + frequency.interval);
          if (nextDate > currentDate) {
            dates.push(new Date(nextDate));
          }
        }
        break;
        
      case "weeks":
        while (nextDate <= endDate) {
          nextDate = new Date(nextDate);
          nextDate.setDate(nextDate.getDate() + (frequency.interval * 7));
          if (nextDate > currentDate) {
            dates.push(new Date(nextDate));
          }
        }
        break;
        
      case "months":
        while (nextDate <= endDate) {
          nextDate = new Date(nextDate);
          nextDate.setMonth(nextDate.getMonth() + frequency.interval);
          if (nextDate > currentDate) {
            dates.push(new Date(nextDate));
          }
        }
        break;
        
      case "years":
        while (nextDate <= endDate) {
          nextDate = new Date(nextDate);
          nextDate.setFullYear(nextDate.getFullYear() + frequency.interval);
          if (nextDate > currentDate) {
            dates.push(new Date(nextDate));
          }
        }
        break;
        
      case "hours":
        // For hour-based maintenance, we need usage data
        // This is a simplified implementation
        while (nextDate <= endDate) {
          nextDate = new Date(nextDate);
          nextDate.setHours(nextDate.getHours() + frequency.interval);
          if (nextDate > currentDate) {
            dates.push(new Date(nextDate));
          }
        }
        break;
        
      case "cycles":
        // For cycle-based maintenance, we need usage/cycle data
        // This would require integration with asset usage tracking
        break;
        
      case "condition":
        // For condition-based maintenance, we need sensor data
        // This would require integration with IoT/sensor systems
        break;
    }
    
    return dates;
  }
  
  /**
   * Generate maintenance tasks for all assets of a specific type
   */
  static async generateTasksForAssetType(assetTypeId: string): Promise<MaintenanceTaskGenerationResult> {
    const result: MaintenanceTaskGenerationResult = {
      tasksGenerated: 0,
      errors: [],
      warnings: [],
    };
    
    try {
      const assets = await prisma.asset.findMany({
        where: { assetTypeId },
        include: {
          assetType: {
            include: {
              maintenanceSchedules: true,
            },
          },
        },
      });
      
      for (const asset of assets) {
        if (!asset.assetType?.maintenanceSchedules) continue;
        
        for (const schedule of asset.assetType.maintenanceSchedules) {
          // Get last maintenance date for this schedule
          const lastMaintenance = await prisma.maintenanceTask.findFirst({
            where: {
              assetId: asset.id,
              scheduleId: schedule.id,
              status: "completed",
            },
            orderBy: {
              completedDate: "desc",
            },
          });
          
          const taskResult = await this.generateMaintenanceTasks({
            assetId: asset.id,
            scheduleId: schedule.id,
            schedule: convertDatabaseScheduleToMaintenanceSchedule(schedule),
            lastMaintenanceDate: lastMaintenance?.completedDate,
          });
          
          result.tasksGenerated += taskResult.tasksGenerated;
          result.errors.push(...taskResult.errors);
          result.warnings.push(...taskResult.warnings);
        }
      }
      
    } catch (error) {
      result.errors.push(`Failed to generate tasks for asset type: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    
    return result;
  }
  
  /**
   * Complete a maintenance task
   */
  static async completeMaintenanceTask(
    taskId: string,
    completionData: {
      completedBy: string;
      completionNotes?: string;
      actualDuration?: number;
      actualCost?: number;
      checklistResults?: Record<string, boolean>;
    }
  ): Promise<void> {
    const { completedBy, completionNotes, actualDuration, actualCost, checklistResults } = completionData;
    
    await prisma.$transaction(async (tx) => {
      // Update the task
      await tx.maintenanceTask.update({
        where: { id: taskId },
        data: {
          status: "completed",
          completedDate: new Date(),
          completionNotes,
          actualDuration,
          actualCost,
        },
      });
      
      // Get task details for logging
      const task = await tx.maintenanceTask.findUnique({
        where: { id: taskId },
      });
      
      if (task) {
        // Log the maintenance in asset maintenance records
        await tx.assetMaintenance.create({
          data: {
            assetId: task.assetId,
            type: task.type,
            scheduledDate: task.scheduledDate,
            completedDate: new Date(),
            assignedTo: completedBy,
            notes: completionNotes,
            status: "completed",
          },
        });
        
        // Log in operation history
        await tx.assetOperationHistory.create({
          data: {
            assetId: task.assetId,
            operationType: "maintenance.log",
            formData: JSON.stringify({
              taskId,
              type: task.type,
              completionNotes,
              actualDuration,
              actualCost,
              checklistResults,
            }),
            performedBy: completedBy,
            status: "completed",
          },
        });
      }
    });
  }
  
  /**
   * Get maintenance metrics for an asset or asset type
   */
  static async getMaintenanceMetrics(
    filters: {
      assetId?: string;
      assetTypeId?: string;
      dateFrom?: Date;
      dateTo?: Date;
    }
  ): Promise<MaintenanceMetrics> {
    const whereClause: any = {};
    
    if (filters.assetId) {
      whereClause.assetId = filters.assetId;
    }
    
    if (filters.assetTypeId) {
      whereClause.asset = {
        assetTypeId: filters.assetTypeId,
      };
    }
    
    if (filters.dateFrom || filters.dateTo) {
      whereClause.scheduledDate = {};
      if (filters.dateFrom) {
        whereClause.scheduledDate.gte = filters.dateFrom;
      }
      if (filters.dateTo) {
        whereClause.scheduledDate.lte = filters.dateTo;
      }
    }
    
    const tasks = await prisma.maintenanceTask.findMany({
      where: whereClause,
      include: {
        asset: true,
      },
    });
    
    const now = new Date();
    const completedTasks = tasks.filter(task => task.status === "completed");
    const overdueTasks = tasks.filter(task => task.status !== "completed" && task.dueDate < now);
    const upcomingTasks = tasks.filter(task => task.status === "scheduled" && task.scheduledDate > now);
    
    // Calculate average completion time
    const completionTimes = completedTasks
      .filter(task => task.actualDuration)
      .map(task => task.actualDuration!);
    const averageCompletionTime = completionTimes.length > 0
      ? completionTimes.reduce((sum, time) => sum + time, 0) / completionTimes.length
      : 0;
    
    // Calculate total cost
    const totalCost = completedTasks
      .filter(task => task.actualCost)
      .reduce((sum, task) => sum + task.actualCost!, 0);
    
    // Group by type
    const tasksByType = tasks.reduce((acc, task) => {
      acc[task.type] = (acc[task.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    // Group by priority
    const tasksByPriority = tasks.reduce((acc, task) => {
      acc[task.priority] = (acc[task.priority] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return {
      totalTasks: tasks.length,
      completedTasks: completedTasks.length,
      overdueTasks: overdueTasks.length,
      upcomingTasks: upcomingTasks.length,
      averageCompletionTime,
      totalCost,
      tasksByType,
      tasksByPriority,
    };
  }
  
  /**
   * Get overdue maintenance tasks
   */
  static async getOverdueTasks(
    filters?: {
      assetId?: string;
      assetTypeId?: string;
      priority?: string;
    }
  ): Promise<any[]> {
    const whereClause: any = {
      status: {
        not: "completed",
      },
      dueDate: {
        lt: new Date(),
      },
    };
    
    if (filters?.assetId) {
      whereClause.assetId = filters.assetId;
    }
    
    if (filters?.assetTypeId) {
      whereClause.asset = {
        assetTypeId: filters.assetTypeId,
      };
    }
    
    if (filters?.priority) {
      whereClause.priority = filters.priority;
    }
    
    return prisma.maintenanceTask.findMany({
      where: whereClause,
      include: {
        asset: {
          include: {
            assetType: true,
          },
        },
      },
      orderBy: {
        dueDate: "asc",
      },
    });
  }
  
  /**
   * Schedule predictive maintenance based on conditions
   */
  static async schedulePredictiveMaintenance(
    assetId: string,
    conditions: {
      metric: string;
      value: number;
      threshold: number;
      operator: "greater_than" | "less_than" | "equals";
    }[]
  ): Promise<void> {
    // Get asset with maintenance schedules
    const asset = await prisma.asset.findUnique({
      where: { id: assetId },
      include: {
        assetType: {
          include: {
            maintenanceSchedules: true,
          },
        },
      },
    });
    
    if (!asset?.assetType?.maintenanceSchedules) return;
    
    // Find predictive maintenance schedules
    const predictiveSchedules = asset.assetType.maintenanceSchedules.filter(
      schedule => schedule.type === "predictive" && schedule.isActive
    );
    
    for (const schedule of predictiveSchedules) {
      // Check if conditions match schedule triggers
      const shouldSchedule = conditions.some(condition => {
        return this.evaluateMaintenanceCondition(condition, schedule.triggers);
      });
      
      if (shouldSchedule) {
        // Check if task already exists
        const existingTask = await prisma.maintenanceTask.findFirst({
          where: {
            assetId,
            scheduleId: schedule.id,
            status: {
              in: ["scheduled", "in_progress"],
            },
          },
        });
        
        if (!existingTask) {
          // Create predictive maintenance task
          await prisma.maintenanceTask.create({
            data: {
              assetId,
              scheduleId: schedule.id,
              title: `Predictive: ${schedule.name}`,
              description: `Triggered by condition: ${conditions.map(c => `${c.metric} ${c.operator} ${c.threshold}`).join(", ")}`,
              type: "predictive",
              priority: "high",
              scheduledDate: new Date(),
              dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Due in 24 hours
              estimatedDuration: schedule.estimatedDuration,
              estimatedCost: schedule.estimatedCost,
              instructions: schedule.instructions,
              checklistItems: JSON.stringify(schedule.checklistItems),
            },
          });
        }
      }
    }
  }
  
  /**
   * Evaluate maintenance condition against triggers
   */
  private static evaluateMaintenanceCondition(
    condition: {
      metric: string;
      value: number;
      threshold: number;
      operator: "greater_than" | "less_than" | "equals";
    },
    triggers: MaintenanceTrigger[]
  ): boolean {
    return triggers.some(trigger => {
      if (trigger.type !== "condition") return false;
      
      const triggerMetric = trigger.parameters.metric;
      const triggerThreshold = trigger.parameters.threshold;
      const triggerOperator = trigger.parameters.operator;
      
      return (
        triggerMetric === condition.metric &&
        triggerOperator === condition.operator &&
        this.compareValues(condition.value, triggerThreshold, condition.operator)
      );
    });
  }
  
  /**
   * Compare values based on operator
   */
  private static compareValues(
    value: number,
    threshold: number,
    operator: "greater_than" | "less_than" | "equals"
  ): boolean {
    switch (operator) {
      case "greater_than":
        return value > threshold;
      case "less_than":
        return value < threshold;
      case "equals":
        return value === threshold;
      default:
        return false;
    }
  }
}