// Main Prisma Database Seeding Script
// Comprehensive seeding for WizeAssets ERP system

import { PrismaClient } from '@prisma/client';
import { seedAssetModules } from './seed-modules';

const prisma = new PrismaClient();

async function seedUsers() {
  console.log('👥 Seeding users...');
  
  // Create admin user
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      id: 'admin-user-1',
      email: '<EMAIL>',
      name: 'System Administrator',
      role: 'ADMIN',
      isActive: true,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date()
    }
  });

  // Create IT manager
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      id: 'it-manager-1',
      email: '<EMAIL>',
      name: '<PERSON>',
      role: '<PERSON><PERSON><PERSON><PERSON>',
      department: 'IT',
      isActive: true,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date()
    }
  });

  // Create fleet manager
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      id: 'fleet-manager-1',
      email: '<EMAIL>',
      name: 'Sarah Wilson',
      role: 'MANAGER',
      department: 'Fleet',
      isActive: true,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date()
    }
  });

  // Create regular users
  const regularUsers = [
    { email: '<EMAIL>', name: 'Mike Johnson', department: 'Operations' },
    { email: '<EMAIL>', name: 'Lisa Brown', department: 'Finance' },
    { email: '<EMAIL>', name: 'David Garcia', department: 'Maintenance' },
    { email: '<EMAIL>', name: 'Emma Davis', department: 'HR' }
  ];

  for (const user of regularUsers) {
    await prisma.user.upsert({
      where: { email: user.email },
      update: {},
      create: {
        id: `user-${user.email.split('@')[0].replace('.', '-')}`,
        email: user.email,
        name: user.name,
        role: 'USER',
        department: user.department,
        isActive: true,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date()
      }
    });
  }

  console.log('✅ Users seeded successfully');
}

async function seedOrganizationData() {
  console.log('🏢 Seeding organization data...');

  // Create departments
  const departments = [
    { name: 'IT', description: 'Information Technology' },
    { name: 'Fleet', description: 'Vehicle Fleet Management' },
    { name: 'Operations', description: 'Operations and Logistics' },
    { name: 'Finance', description: 'Financial Management' },
    { name: 'Maintenance', description: 'Asset Maintenance' },
    { name: 'HR', description: 'Human Resources' },
    { name: 'Procurement', description: 'Procurement and Purchasing' },
    { name: 'Security', description: 'Security and Safety' }
  ];

  for (const dept of departments) {
    await prisma.department.upsert({
      where: { name: dept.name },
      update: {},
      create: {
        id: `dept-${dept.name.toLowerCase()}`,
        name: dept.name,
        description: dept.description,
        isActive: true,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date()
      }
    });
  }

  // Create locations
  const locations = [
    { name: 'Headquarters', address: '123 Business Ave, Downtown, NY 10001', type: 'Office' },
    { name: 'Warehouse A', address: '456 Industrial Blvd, Industrial Park, NY 10002', type: 'Warehouse' },
    { name: 'Data Center', address: '789 Tech Drive, Tech Park, NY 10003', type: 'Data Center' },
    { name: 'Manufacturing Plant', address: '321 Factory Road, Industrial Zone, NY 10004', type: 'Manufacturing' }
  ];

  for (const location of locations) {
    await prisma.location.upsert({
      where: { name: location.name },
      update: {},
      create: {
        id: `loc-${location.name.toLowerCase().replace(/\s+/g, '-')}`,
        name: location.name,
        address: location.address,
        type: location.type,
        isActive: true,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date()
      }
    });
  }

  console.log('✅ Organization data seeded successfully');
}

async function seedAssetTypes() {
  console.log('📦 Seeding asset types...');

  const assetTypes = [
    { name: 'IT Equipment', category: 'Hardware', description: 'Computers, servers, and IT hardware' },
    { name: 'Software Licenses', category: 'Software', description: 'Software licenses and subscriptions' },
    { name: 'Vehicles', category: 'Vehicle', description: 'Company vehicles and fleet assets' },
    { name: 'Real Estate', category: 'Property', description: 'Buildings, offices, and real estate' },
    { name: 'Manufacturing Equipment', category: 'Machinery', description: 'Production and manufacturing equipment' },
    { name: 'Office Furniture', category: 'Furniture', description: 'Desks, chairs, and office furniture' },
    { name: 'Tools & Equipment', category: 'Tools', description: 'Hand tools and equipment' }
  ];

  for (const assetType of assetTypes) {
    await prisma.assetType.upsert({
      where: { name: assetType.name },
      update: {},
      create: {
        id: `type-${assetType.name.toLowerCase().replace(/\s+/g, '-')}`,
        name: assetType.name,
        category: assetType.category,
        description: assetType.description,
        isActive: true,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date()
      }
    });
  }

  console.log('✅ Asset types seeded successfully');
}

async function main() {
  console.log('🌱 Starting comprehensive database seeding...');
  
  try {
    // Seed in order of dependencies
    await seedUsers();
    await seedOrganizationData();
    await seedAssetTypes();
    await seedAssetModules();

    console.log('🎉 All seeding completed successfully!');
    console.log('');
    console.log('📊 Seeding Summary:');
    console.log('  - Users: Admin, managers, and regular users');
    console.log('  - Departments: IT, Fleet, Operations, Finance, etc.');
    console.log('  - Locations: Headquarters, warehouses, data centers');
    console.log('  - Asset Types: IT Equipment, Software, Vehicles, etc.');
    console.log('  - Asset Modules: 5 comprehensive modules with sample data');
    console.log('');
    console.log('🚀 Your WizeAssets ERP system is ready for development!');

  } catch (error) {
    console.error('❌ Seeding failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run seeding
main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });

export default main;
