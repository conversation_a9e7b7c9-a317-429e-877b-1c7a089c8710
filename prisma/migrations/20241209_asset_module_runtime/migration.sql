-- Asset Module Runtime System Database Schema
-- This migration adds tables to support the Asset Module Runtime System

-- Create base AssetModule table first
CREATE TABLE "AssetModule" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "version" TEXT NOT NULL DEFAULT '1.0.0',
    "author" TEXT,
    "authorId" TEXT,
    "tags" TEXT[],
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "isBuiltIn" BOOLEAN NOT NULL DEFAULT false,
    "fields" JSONB NOT NULL DEFAULT '[]',
    "logic" JSONB,
    "rendering" JSONB,
    "validation" JSONB,
    "dependencies" TEXT[],
    "compatibleAssetTypes" TEXT[],
    "requiredPermissions" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AssetModule_pkey" PRIMARY KEY ("id")
);

-- Module Execution Logs
CREATE TABLE "ModuleExecutionLog" (
    "id" TEXT NOT NULL,
    "moduleId" TEXT NOT NULL,
    "assetId" TEXT,
    "assetTypeId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "executionId" TEXT NOT NULL,
    "success" BOOLEAN NOT NULL DEFAULT false,
    "executionTime" INTEGER NOT NULL DEFAULT 0,
    "inputData" TEXT,
    "outputData" TEXT,
    "errors" TEXT,
    "warnings" TEXT,
    "metadata" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ModuleExecutionLog_pkey" PRIMARY KEY ("id")
);

-- Module Validation Logs
CREATE TABLE "ModuleValidationLog" (
    "id" TEXT NOT NULL,
    "moduleId" TEXT NOT NULL,
    "assetTypeId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "valid" BOOLEAN NOT NULL DEFAULT false,
    "validationTime" INTEGER NOT NULL DEFAULT 0,
    "fieldsValidated" INTEGER NOT NULL DEFAULT 0,
    "rulesExecuted" INTEGER NOT NULL DEFAULT 0,
    "inputData" TEXT,
    "errors" TEXT,
    "warnings" TEXT,
    "metadata" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ModuleValidationLog_pkey" PRIMARY KEY ("id")
);

-- Module Render Logs
CREATE TABLE "ModuleRenderLog" (
    "id" TEXT NOT NULL,
    "moduleId" TEXT NOT NULL,
    "assetTypeId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "renderTime" INTEGER NOT NULL DEFAULT 0,
    "fieldsRendered" INTEGER NOT NULL DEFAULT 0,
    "cacheHits" INTEGER NOT NULL DEFAULT 0,
    "cacheMisses" INTEGER NOT NULL DEFAULT 0,
    "inputData" TEXT,
    "options" TEXT,
    "metadata" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ModuleRenderLog_pkey" PRIMARY KEY ("id")
);

-- Module Deployment Logs
CREATE TABLE "ModuleDeploymentLog" (
    "id" TEXT NOT NULL,
    "moduleId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "deploymentId" TEXT NOT NULL,
    "success" BOOLEAN NOT NULL DEFAULT false,
    "version" TEXT NOT NULL,
    "deploymentTime" INTEGER NOT NULL DEFAULT 0,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "errors" TEXT,
    "warnings" TEXT,
    "options" TEXT,
    "metadata" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ModuleDeploymentLog_pkey" PRIMARY KEY ("id")
);

-- System Action Logs (for admin operations)
CREATE TABLE "SystemActionLog" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "parameters" TEXT,
    "result" TEXT,
    "success" BOOLEAN NOT NULL DEFAULT false,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SystemActionLog_pkey" PRIMARY KEY ("id")
);

-- Module Permissions (for fine-grained access control)
CREATE TABLE "ModulePermission" (
    "id" TEXT NOT NULL,
    "moduleId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "permission" TEXT NOT NULL, -- 'execute', 'validate', 'render', 'deploy', 'undeploy', etc.
    "grantedBy" TEXT NOT NULL,
    "grantedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" TIMESTAMP(3),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ModulePermission_pkey" PRIMARY KEY ("id")
);

-- Module Dependencies (for dependency tracking)
CREATE TABLE "ModuleDependency" (
    "id" TEXT NOT NULL,
    "moduleId" TEXT NOT NULL,
    "dependsOnModuleId" TEXT NOT NULL,
    "version" TEXT NOT NULL DEFAULT '*',
    "type" TEXT NOT NULL DEFAULT 'required', -- 'required', 'optional', 'development'
    "resolved" BOOLEAN NOT NULL DEFAULT false,
    "resolvedVersion" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ModuleDependency_pkey" PRIMARY KEY ("id")
);

-- Module Performance Metrics
CREATE TABLE "ModulePerformanceMetric" (
    "id" TEXT NOT NULL,
    "moduleId" TEXT NOT NULL,
    "metricType" TEXT NOT NULL, -- 'execution', 'rendering', 'validation', 'memory', etc.
    "value" DOUBLE PRECISION NOT NULL,
    "unit" TEXT NOT NULL, -- 'ms', 'bytes', 'count', etc.
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "metadata" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ModulePerformanceMetric_pkey" PRIMARY KEY ("id")
);

-- Module Security Events
CREATE TABLE "ModuleSecurityEvent" (
    "id" TEXT NOT NULL,
    "moduleId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "eventType" TEXT NOT NULL, -- 'permission_denied', 'sandbox_violation', 'rate_limit_exceeded', etc.
    "severity" TEXT NOT NULL DEFAULT 'info', -- 'info', 'warning', 'error', 'critical'
    "description" TEXT NOT NULL,
    "metadata" TEXT,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ModuleSecurityEvent_pkey" PRIMARY KEY ("id")
);

-- Add new fields to existing AssetModule table
ALTER TABLE "AssetModule" ADD COLUMN IF NOT EXISTS "deploymentStatus" TEXT DEFAULT 'draft';
ALTER TABLE "AssetModule" ADD COLUMN IF NOT EXISTS "deployedAt" TIMESTAMP(3);
ALTER TABLE "AssetModule" ADD COLUMN IF NOT EXISTS "deployedBy" TEXT;
ALTER TABLE "AssetModule" ADD COLUMN IF NOT EXISTS "undeployedAt" TIMESTAMP(3);
ALTER TABLE "AssetModule" ADD COLUMN IF NOT EXISTS "undeployedBy" TEXT;
ALTER TABLE "AssetModule" ADD COLUMN IF NOT EXISTS "runtimeVersion" TEXT;
ALTER TABLE "AssetModule" ADD COLUMN IF NOT EXISTS "lastExecutedAt" TIMESTAMP(3);
ALTER TABLE "AssetModule" ADD COLUMN IF NOT EXISTS "executionCount" INTEGER DEFAULT 0;
ALTER TABLE "AssetModule" ADD COLUMN IF NOT EXISTS "averageExecutionTime" DOUBLE PRECISION DEFAULT 0;
ALTER TABLE "AssetModule" ADD COLUMN IF NOT EXISTS "successRate" DOUBLE PRECISION DEFAULT 0;

-- Create indexes for performance
CREATE INDEX "ModuleExecutionLog_moduleId_idx" ON "ModuleExecutionLog"("moduleId");
CREATE INDEX "ModuleExecutionLog_userId_idx" ON "ModuleExecutionLog"("userId");
CREATE INDEX "ModuleExecutionLog_createdAt_idx" ON "ModuleExecutionLog"("createdAt");
CREATE INDEX "ModuleExecutionLog_success_idx" ON "ModuleExecutionLog"("success");

CREATE INDEX "ModuleValidationLog_moduleId_idx" ON "ModuleValidationLog"("moduleId");
CREATE INDEX "ModuleValidationLog_userId_idx" ON "ModuleValidationLog"("userId");
CREATE INDEX "ModuleValidationLog_createdAt_idx" ON "ModuleValidationLog"("createdAt");
CREATE INDEX "ModuleValidationLog_valid_idx" ON "ModuleValidationLog"("valid");

CREATE INDEX "ModuleRenderLog_moduleId_idx" ON "ModuleRenderLog"("moduleId");
CREATE INDEX "ModuleRenderLog_userId_idx" ON "ModuleRenderLog"("userId");
CREATE INDEX "ModuleRenderLog_createdAt_idx" ON "ModuleRenderLog"("createdAt");

CREATE INDEX "ModuleDeploymentLog_moduleId_idx" ON "ModuleDeploymentLog"("moduleId");
CREATE INDEX "ModuleDeploymentLog_userId_idx" ON "ModuleDeploymentLog"("userId");
CREATE INDEX "ModuleDeploymentLog_createdAt_idx" ON "ModuleDeploymentLog"("createdAt");
CREATE INDEX "ModuleDeploymentLog_status_idx" ON "ModuleDeploymentLog"("status");

CREATE INDEX "SystemActionLog_userId_idx" ON "SystemActionLog"("userId");
CREATE INDEX "SystemActionLog_action_idx" ON "SystemActionLog"("action");
CREATE INDEX "SystemActionLog_timestamp_idx" ON "SystemActionLog"("timestamp");

CREATE INDEX "ModulePermission_moduleId_idx" ON "ModulePermission"("moduleId");
CREATE INDEX "ModulePermission_userId_idx" ON "ModulePermission"("userId");
CREATE INDEX "ModulePermission_permission_idx" ON "ModulePermission"("permission");
CREATE INDEX "ModulePermission_isActive_idx" ON "ModulePermission"("isActive");

CREATE INDEX "ModuleDependency_moduleId_idx" ON "ModuleDependency"("moduleId");
CREATE INDEX "ModuleDependency_dependsOnModuleId_idx" ON "ModuleDependency"("dependsOnModuleId");
CREATE INDEX "ModuleDependency_resolved_idx" ON "ModuleDependency"("resolved");

CREATE INDEX "ModulePerformanceMetric_moduleId_idx" ON "ModulePerformanceMetric"("moduleId");
CREATE INDEX "ModulePerformanceMetric_metricType_idx" ON "ModulePerformanceMetric"("metricType");
CREATE INDEX "ModulePerformanceMetric_timestamp_idx" ON "ModulePerformanceMetric"("timestamp");

CREATE INDEX "ModuleSecurityEvent_moduleId_idx" ON "ModuleSecurityEvent"("moduleId");
CREATE INDEX "ModuleSecurityEvent_userId_idx" ON "ModuleSecurityEvent"("userId");
CREATE INDEX "ModuleSecurityEvent_eventType_idx" ON "ModuleSecurityEvent"("eventType");
CREATE INDEX "ModuleSecurityEvent_severity_idx" ON "ModuleSecurityEvent"("severity");
CREATE INDEX "ModuleSecurityEvent_timestamp_idx" ON "ModuleSecurityEvent"("timestamp");

CREATE INDEX "AssetModule_deploymentStatus_idx" ON "AssetModule"("deploymentStatus");
CREATE INDEX "AssetModule_deployedAt_idx" ON "AssetModule"("deployedAt");
CREATE INDEX "AssetModule_lastExecutedAt_idx" ON "AssetModule"("lastExecutedAt");

-- Create unique constraints
CREATE UNIQUE INDEX "ModulePermission_moduleId_userId_permission_key" ON "ModulePermission"("moduleId", "userId", "permission");
CREATE UNIQUE INDEX "ModuleDependency_moduleId_dependsOnModuleId_key" ON "ModuleDependency"("moduleId", "dependsOnModuleId");

-- Add foreign key constraints
ALTER TABLE "ModuleExecutionLog" ADD CONSTRAINT "ModuleExecutionLog_moduleId_fkey" FOREIGN KEY ("moduleId") REFERENCES "AssetModule"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ModuleExecutionLog" ADD CONSTRAINT "ModuleExecutionLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "ModuleValidationLog" ADD CONSTRAINT "ModuleValidationLog_moduleId_fkey" FOREIGN KEY ("moduleId") REFERENCES "AssetModule"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ModuleValidationLog" ADD CONSTRAINT "ModuleValidationLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "ModuleRenderLog" ADD CONSTRAINT "ModuleRenderLog_moduleId_fkey" FOREIGN KEY ("moduleId") REFERENCES "AssetModule"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ModuleRenderLog" ADD CONSTRAINT "ModuleRenderLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "ModuleDeploymentLog" ADD CONSTRAINT "ModuleDeploymentLog_moduleId_fkey" FOREIGN KEY ("moduleId") REFERENCES "AssetModule"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ModuleDeploymentLog" ADD CONSTRAINT "ModuleDeploymentLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "SystemActionLog" ADD CONSTRAINT "SystemActionLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "ModulePermission" ADD CONSTRAINT "ModulePermission_moduleId_fkey" FOREIGN KEY ("moduleId") REFERENCES "AssetModule"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ModulePermission" ADD CONSTRAINT "ModulePermission_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ModulePermission" ADD CONSTRAINT "ModulePermission_grantedBy_fkey" FOREIGN KEY ("grantedBy") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "ModuleDependency" ADD CONSTRAINT "ModuleDependency_moduleId_fkey" FOREIGN KEY ("moduleId") REFERENCES "AssetModule"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ModuleDependency" ADD CONSTRAINT "ModuleDependency_dependsOnModuleId_fkey" FOREIGN KEY ("dependsOnModuleId") REFERENCES "AssetModule"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "ModulePerformanceMetric" ADD CONSTRAINT "ModulePerformanceMetric_moduleId_fkey" FOREIGN KEY ("moduleId") REFERENCES "AssetModule"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "ModuleSecurityEvent" ADD CONSTRAINT "ModuleSecurityEvent_moduleId_fkey" FOREIGN KEY ("moduleId") REFERENCES "AssetModule"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "ModuleSecurityEvent" ADD CONSTRAINT "ModuleSecurityEvent_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
