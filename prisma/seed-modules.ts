// Prisma Database Seeding Script for Asset Modules
// Seeds the database with comprehensive asset module examples

import { PrismaClient } from '@prisma/client';
import { allSeedModules, sampleAssetInstances } from '../lib/data/seed-data-initializer';

const prisma = new PrismaClient();

async function seedAssetModules() {
  console.log('🌱 Starting Asset Module database seeding...');

  try {
    // Clear existing data (optional - uncomment if needed)
    // console.log('🧹 Clearing existing asset modules...');
    // await prisma.assetModule.deleteMany({});
    // await prisma.assetInstance.deleteMany({});

    // Seed asset modules
    console.log('📦 Seeding asset modules...');
    
    for (const module of allSeedModules) {
      console.log(`  - Creating module: ${module.name}`);
      
      await prisma.assetModule.upsert({
        where: { id: module.id },
        update: {
          name: module.name,
          description: module.description,
          category: module.category,
          version: module.version,
          author: module.author,
          tags: module.tags,
          isActive: module.isActive,
          isPublic: module.isPublic,
          fields: module.fields as any,
          logic: module.logic as any,
          validation: module.validation as any,
          rendering: module.rendering as any,
          permissions: module.permissions as any,
          integrations: module.integrations as any,
          metadata: module.metadata as any,
          updatedAt: module.updatedAt
        },
        create: {
          id: module.id,
          name: module.name,
          description: module.description,
          category: module.category,
          version: module.version,
          author: module.author,
          tags: module.tags,
          isActive: module.isActive,
          isPublic: module.isPublic,
          fields: module.fields as any,
          logic: module.logic as any,
          validation: module.validation as any,
          rendering: module.rendering as any,
          permissions: module.permissions as any,
          integrations: module.integrations as any,
          metadata: module.metadata as any,
          createdAt: module.createdAt,
          updatedAt: module.updatedAt
        }
      });
    }

    // Seed sample asset instances
    console.log('📋 Seeding sample asset instances...');
    
    for (const [moduleId, instances] of Object.entries(sampleAssetInstances)) {
      console.log(`  - Creating ${instances.length} instances for module: ${moduleId}`);
      
      for (const instance of instances) {
        await prisma.assetInstance.upsert({
          where: { id: instance.id },
          update: {
            moduleId: instance.moduleId,
            data: instance.data as any,
            updatedAt: instance.updatedAt
          },
          create: {
            id: instance.id,
            moduleId: instance.moduleId,
            data: instance.data as any,
            createdAt: instance.createdAt,
            updatedAt: instance.updatedAt
          }
        });
      }
    }

    // Create module usage statistics
    console.log('📊 Creating module usage statistics...');
    
    for (const module of allSeedModules) {
      const instanceCount = sampleAssetInstances[module.id as keyof typeof sampleAssetInstances]?.length || 0;
      
      await prisma.moduleUsageStats.upsert({
        where: { moduleId: module.id },
        update: {
          totalInstances: instanceCount,
          activeUsers: module.metadata?.usage?.activeUsers || 0,
          lastUsed: module.metadata?.usage?.lastUsed || module.updatedAt,
          updatedAt: new Date()
        },
        create: {
          moduleId: module.id,
          totalInstances: instanceCount,
          activeUsers: module.metadata?.usage?.activeUsers || 0,
          lastUsed: module.metadata?.usage?.lastUsed || module.updatedAt,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }

    console.log('✅ Asset Module seeding completed successfully!');
    console.log(`📦 Seeded ${allSeedModules.length} modules`);
    console.log(`📋 Seeded ${Object.values(sampleAssetInstances).reduce((sum, instances) => sum + instances.length, 0)} asset instances`);

  } catch (error) {
    console.error('❌ Error seeding asset modules:', error);
    throw error;
  }
}

async function main() {
  try {
    await seedAssetModules();
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  main();
}

export { seedAssetModules };
export default main;
