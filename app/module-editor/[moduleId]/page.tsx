"use client";

import React, { useState, useEffect, useCallback } from "react";
import { use<PERSON>ara<PERSON>, useRouter, useSearchParams } from "next/navigation";
import { AssetModuleEditor } from "@/components/asset-modules/asset-module-editor";
import { AssetModule, ModuleTemplate } from "@/lib/types/asset-modules";
import { toast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { AlertCircle, ArrowLeft, RefreshCw } from "lucide-react";

interface ModuleEditorPageProps {
  params: {
    moduleId: string;
  };
}

export default function ModuleEditorPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const moduleId = params.moduleId as string;
  const templateId = searchParams.get('template');

  const [module, setModule] = useState<AssetModule | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Load module data
  useEffect(() => {
    if (moduleId === 'new') {
      // Create new module, optionally from template
      if (templateId) {
        loadModuleFromTemplate(templateId);
      } else {
        setModule(createDefaultModule());
        setIsLoading(false);
      }
    } else {
      loadModule(moduleId);
    }
  }, [moduleId, templateId]);

  // Handle beforeunload to warn about unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);

  const loadModule = async (id: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/asset-modules/${id}`);
      if (!response.ok) {
        throw new Error("Failed to load module");
      }

      const moduleData = await response.json();
      setModule(moduleData);
    } catch (error) {
      console.error("Error loading module:", error);
      setError("Failed to load module. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const loadModuleFromTemplate = async (templateId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/asset-module-templates/${templateId}`);
      if (!response.ok) {
        throw new Error("Failed to load template");
      }

      const template: ModuleTemplate = await response.json();

      // Create module from template
      const newModule = createModuleFromTemplate(template);
      setModule(newModule);
    } catch (error) {
      console.error("Error loading template:", error);
      setError("Failed to load template. Creating default module instead.");
      setModule(createDefaultModule());
    } finally {
      setIsLoading(false);
    }
  };

  const handleModuleUpdate = useCallback((updates: Partial<AssetModule>) => {
    if (!module) return;

    setModule(prev => prev ? { ...prev, ...updates } : null);
    setHasUnsavedChanges(true);
  }, [module]);

  const handleSave = useCallback(async (moduleToSave: AssetModule) => {
    try {
      const method = moduleId === 'new' ? 'POST' : 'PUT';
      const url = moduleId === 'new' ? '/api/asset-modules' : `/api/asset-modules/${moduleId}`;

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...moduleToSave,
          updatedAt: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save module');
      }

      const savedModule = await response.json();
      setModule(savedModule);
      setHasUnsavedChanges(false);

      toast({
        title: "Success",
        description: "Module saved successfully.",
      });

      // If this was a new module, redirect to the saved module's URL
      if (moduleId === 'new') {
        router.replace(`/module-editor/${savedModule.id}`);
      }

      return savedModule;
    } catch (error) {
      console.error("Error saving module:", error);
      toast({
        title: "Error",
        description: "Failed to save module.",
        variant: "destructive",
      });
      throw error;
    }
  }, [moduleId, router]);

  const handleClose = useCallback(() => {
    if (hasUnsavedChanges) {
      const confirmed = window.confirm(
        'You have unsaved changes. Are you sure you want to leave?'
      );
      if (!confirmed) return;
    }

    // Navigate back to module management
    router.push('/admin/asset-modules');
  }, [hasUnsavedChanges, router]);

  const handleExport = useCallback((moduleToExport: AssetModule) => {
    const dataStr = JSON.stringify(moduleToExport, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `${moduleToExport.name.replace(/\s+/g, '_')}_module.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  }, []);

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <div className="relative">
            <RefreshCw className="h-12 w-12 animate-spin text-primary mx-auto" />
            <div className="absolute inset-0 rounded-full border-2 border-primary/20 animate-pulse"></div>
          </div>
          <div className="space-y-2">
            <h2 className="text-xl font-semibold">Loading Module Editor</h2>
            <p className="text-muted-foreground">
              {moduleId === 'new'
                ? templateId
                  ? 'Preparing template...'
                  : 'Setting up new module...'
                : 'Loading module data...'
              }
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-6 max-w-md">
          <div className="space-y-4">
            <AlertCircle className="h-16 w-16 text-destructive mx-auto" />
            <div className="space-y-2">
              <h1 className="text-2xl font-bold text-destructive">Unable to Load Module</h1>
              <p className="text-muted-foreground">{error}</p>
            </div>
          </div>
          <div className="flex gap-3 justify-center">
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
            <Button
              onClick={() => router.push('/admin/asset-modules')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Modules
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!module) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-6 max-w-md">
          <div className="space-y-4">
            <AlertCircle className="h-16 w-16 text-muted-foreground mx-auto" />
            <div className="space-y-2">
              <h1 className="text-2xl font-bold">Module Not Found</h1>
              <p className="text-muted-foreground">
                The requested module could not be found or may have been deleted.
              </p>
            </div>
          </div>
          <div className="flex gap-3 justify-center">
            <Button
              variant="outline"
              onClick={() => router.push('/module-editor/new')}
            >
              Create New Module
            </Button>
            <Button
              onClick={() => router.push('/admin/asset-modules')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Modules
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-background">
      <AssetModuleEditor
        module={module}
        onUpdate={handleModuleUpdate}
        onSave={handleSave}
        onClose={handleClose}
        onExport={handleExport}
        hasUnsavedChanges={hasUnsavedChanges}
        isNewModule={moduleId === 'new'}
      />
    </div>
  );
}

// Helper function to create a default module
function createDefaultModule(): AssetModule {
  return {
    id: `module-${Date.now()}`,
    name: "New Asset Module",
    version: "1.0.0",
    description: "",
    category: "custom",
    author: "current-user", // TODO: Get from auth context
    tags: [],
    fields: [],
    logic: {
      nodes: [],
      edges: [],
      variables: [],
      functions: [],
    },
    rendering: {
      formLayout: {
          sections: [],
          columns: 1,
          spacing: "normal",
          grouping: [],
          groupingStyle: "custom",
          responsive: false
      },
      displayLayout: {
        views: [],
        defaultView: "card",
      },
      components: [],
    },
    validation: {
      rules: [],
      crossFieldValidation: [],
    },
    isActive: false,
    isBuiltIn: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    usageCount: 0,
    dependencies: [],
    compatibleAssetTypes: [],
    requiredPermissions: [],
  };
}

// Helper function to create a module from a template
function createModuleFromTemplate(template: ModuleTemplate): AssetModule {
  const baseModule = createDefaultModule();

  return {
    ...baseModule,
    id: `module-${Date.now()}`,
    name: template.name,
    description: template.description,
    category: template.category,
    // Merge template module data with defaults
    ...template.module,
    // Ensure required fields are always present
    isActive: false,
    isBuiltIn: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    usageCount: 0,
  };
}
