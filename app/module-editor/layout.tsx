import React from "react";
import type { <PERSON>ada<PERSON> } from "next";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Toaster } from "@/components/ui/toaster";

export const metadata: Metadata = {
  title: "Module Editor - WizeAssets",
  description: "Advanced Asset Module Editor - Create and customize asset modules with visual tools",
};

interface ModuleEditorLayoutProps {
  children: React.ReactNode;
}

export default function ModuleEditorLayout({ children }: ModuleEditorLayoutProps) {
  return (
    <div className="h-screen overflow-hidden bg-background">
      <TooltipProvider>
        {children}
        <Toaster />
      </TooltipProvider>
    </div>
  );
}
