"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function ModuleEditorIndexPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the admin asset modules page
    // The module editor should be accessed via /module-editor/[moduleId]
    router.replace('/admin/asset-modules');
  }, [router]);

  return (
    <div className="h-screen flex items-center justify-center bg-background">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-muted-foreground">Redirecting to module dashboard...</p>
      </div>
    </div>
  );
}
