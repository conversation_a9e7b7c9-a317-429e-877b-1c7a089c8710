// AI Chat API Route for Asset Module Editor
// Handles conversational AI interactions with module context

import { openai } from '@ai-sdk/openai';
import { streamText, tool } from 'ai';
import { z } from 'zod';
import { allModuleAITools } from '@/lib/ai/tools/module-ai-tools';

// Allow streaming responses up to 60 seconds for complex analysis
export const maxDuration = 60;

export async function POST(req: Request) {
  try {
    const { messages, moduleId, moduleData } = await req.json();

    // Parse module data for context
    let moduleContext = '';
    try {
      const module = JSON.parse(moduleData);
      moduleContext = `
Current Module Context:
- Name: ${module.name}
- Category: ${module.category}
- Description: ${module.description}
- Fields: ${module.fields?.length || 0} fields
- Version: ${module.version}
- Author: ${module.author}
- Last Updated: ${module.updatedAt}
      `.trim();
    } catch (error) {
      console.error('Error parsing module data:', error);
    }

    const result = streamText({
      model: openai('gpt-4o'),
      system: `You are an expert AI assistant specializing in asset module design and development. 
      You help users create, analyze, and optimize asset management modules.

      Your capabilities include:
      1. Suggesting appropriate fields for modules
      2. Analyzing module completeness and quality
      3. Generating business logic flows
      4. Validating field configurations
      5. Providing optimization recommendations
      6. Generating documentation

      Always provide practical, actionable advice based on industry best practices.
      When suggesting changes, explain the reasoning and potential impact.
      
      ${moduleContext}
      
      Guidelines:
      - Be concise but thorough in explanations
      - Provide specific examples when helpful
      - Consider user experience and maintainability
      - Suggest industry-standard approaches
      - Highlight potential issues or improvements
      - Use tools when appropriate for complex analysis`,
      
      messages,
      tools: allModuleAITools,
      maxSteps: 5, // Allow multi-step tool usage
      
      onStepFinish({ text, toolCalls, toolResults }) {
        // Log tool usage for debugging
        if (toolCalls.length > 0) {
          console.log('AI used tools:', toolCalls.map(tc => tc.toolName));
        }
      }
    });

    return result.toDataStreamResponse({
      getErrorMessage: (error) => {
        console.error('AI Chat Error:', error);
        return 'I encountered an issue processing your request. Please try again.';
      }
    });

  } catch (error) {
    console.error('API Error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }), 
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
