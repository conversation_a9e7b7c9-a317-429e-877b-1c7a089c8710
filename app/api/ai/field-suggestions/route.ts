// AI Field Suggestions API Route
// Generates intelligent field suggestions for asset modules

import { openai } from '@ai-sdk/openai';
import { streamObject } from 'ai';
import { z } from 'zod';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

// Schema for field suggestions
const FieldSuggestionsSchema = z.object({
  suggestions: z.array(z.object({
    name: z.string().describe('Name of the field (camelCase)'),
    type: z.enum(['text', 'number', 'date', 'boolean', 'select', 'multiselect', 'file', 'url']),
    label: z.string().describe('Human-readable label for the field'),
    description: z.string().describe('Detailed description of the field purpose'),
    required: z.boolean(),
    validation: z.object({
      min: z.number().optional(),
      max: z.number().optional(),
      pattern: z.string().optional(),
      options: z.array(z.string()).optional()
    }).optional(),
    reasoning: z.string().describe('Explanation of why this field is recommended'),
    confidence: z.number().min(0).max(1).describe('Confidence score (0-1) for this suggestion')
  })),
  overallConfidence: z.number().min(0).max(1),
  explanation: z.string().describe('Overall explanation of the field suggestions')
});

export async function POST(req: Request) {
  try {
    const { description, category, existingFields = [], context = '' } = await req.json();

    const result = streamObject({
      model: openai('gpt-4o'),
      schema: FieldSuggestionsSchema,
      system: `You are an expert in asset management systems and data modeling.
      Your task is to suggest appropriate fields for asset modules based on their description and category.
      Consider industry best practices, data relationships, and user experience.
      
      Guidelines:
      - Suggest fields that are commonly needed for the given category
      - Ensure proper data types and validation rules
      - Consider relationships between fields
      - Avoid suggesting fields that already exist
      - Provide clear reasoning for each suggestion
      - Assign realistic confidence scores based on how essential the field is
      - Focus on practical, useful fields that add value`,
      
      prompt: `Generate field suggestions for an asset module with:
      
      Description: ${description}
      Category: ${category}
      Existing fields: ${existingFields.join(', ') || 'None'}
      ${context ? `Additional context: ${context}` : ''}
      
      Provide:
      1. A list of suggested fields with appropriate types and validation
      2. Clear reasoning for each suggestion
      3. Confidence score for each field (0-1)
      4. Overall explanation of your suggestions
      
      Focus on fields that would be essential or highly valuable for tracking and managing assets in this category.`
    });

    return result.toTextStreamResponse();

  } catch (error) {
    console.error('API Error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }), 
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
