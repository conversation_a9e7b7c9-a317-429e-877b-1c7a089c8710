// AI Module Analysis API Route
// Provides comprehensive analysis of asset modules

import { openai } from '@ai-sdk/openai';
import { streamObject } from 'ai';
import { z } from 'zod';

// Allow streaming responses up to 45 seconds for complex analysis
export const maxDuration = 45;

// Schema for module analysis
const ModuleAnalysisSchema = z.object({
  analysis: z.object({
    complexity: z.enum(['low', 'medium', 'high']),
    completeness: z.number().min(0).max(100),
    qualityScore: z.number().min(0).max(100),
    issues: z.array(z.object({
      type: z.enum(['error', 'warning', 'suggestion']),
      message: z.string(),
      field: z.string().optional(),
      severity: z.enum(['low', 'medium', 'high']),
      category: z.string()
    })),
    suggestions: z.array(z.object({
      type: z.enum(['field', 'logic', 'validation', 'rendering', 'performance']),
      title: z.string(),
      description: z.string(),
      impact: z.enum(['low', 'medium', 'high']),
      effort: z.enum(['low', 'medium', 'high'])
    })),
    strengths: z.array(z.string()),
    weaknesses: z.array(z.string())
  }),
  recommendations: z.array(z.object({
    priority: z.enum(['low', 'medium', 'high']),
    title: z.string(),
    description: z.string(),
    category: z.string(),
    estimatedImpact: z.string()
  })),
  benchmarks: z.object({
    industryAverage: z.number().min(0).max(100),
    bestPracticeScore: z.number().min(0).max(100),
    userExperienceScore: z.number().min(0).max(100),
    maintainabilityScore: z.number().min(0).max(100)
  })
});

export async function POST(req: Request) {
  try {
    const { moduleData, analysisType = 'comprehensive', includeComparison = false } = await req.json();

    // Parse module data
    let module;
    try {
      module = JSON.parse(moduleData);
    } catch (error) {
      return new Response(
        JSON.stringify({ error: 'Invalid module data format' }), 
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const result = streamObject({
      model: openai('gpt-4o'),
      schema: ModuleAnalysisSchema,
      system: `You are an expert asset module analyst with deep knowledge of:
      - Asset management best practices
      - Data modeling and validation
      - User experience design
      - System performance optimization
      - Industry standards and compliance
      
      Your analysis should be thorough, actionable, and based on real-world experience.
      Consider both technical aspects and business value.`,
      
      prompt: `Analyze this asset module comprehensively:
      
      Module Data:
      ${JSON.stringify(module, null, 2)}
      
      Provide a detailed analysis including:
      
      1. COMPLEXITY ASSESSMENT:
         - Evaluate the overall complexity (low/medium/high)
         - Consider field count, validation rules, logic flows
      
      2. COMPLETENESS SCORE (0-100):
         - How complete is this module for its intended purpose?
         - Are essential fields missing?
         - Is validation adequate?
      
      3. QUALITY SCORE (0-100):
         - Overall quality assessment
         - Consider naming conventions, structure, validation
      
      4. ISSUES IDENTIFICATION:
         - Find specific problems (errors, warnings, suggestions)
         - Categorize by severity and type
         - Include field-specific issues where applicable
      
      5. IMPROVEMENT SUGGESTIONS:
         - Specific actionable recommendations
         - Categorize by type (field, logic, validation, etc.)
         - Estimate impact and effort required
      
      6. STRENGTHS & WEAKNESSES:
         - What's working well?
         - What needs improvement?
      
      7. RECOMMENDATIONS:
         - Prioritized list of improvements
         - Include estimated impact
      
      8. BENCHMARKS:
         - Compare against industry standards
         - Score different aspects (UX, maintainability, etc.)
      
      Be specific, actionable, and consider the module's category: ${module.category}`
    });

    return result.toTextStreamResponse();

  } catch (error) {
    console.error('API Error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }), 
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
