"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { AssetModuleDashboard } from "@/components/asset-modules/asset-module-dashboard";
import { ModuleTemplate } from "@/lib/types/asset-modules";

export default function AssetModulesPage() {
  const router = useRouter();

  const handleCreateModule = (template?: ModuleTemplate) => {
    // Navigate to the module editor with 'new' as the moduleId
    if (template) {
      // For templates, we could pass template data via query params or state
      router.push(`/module-editor/new?template=${template.id}`);
    } else {
      router.push('/module-editor/new');
    }
  };

  const handleEditModule = (moduleId: string) => {
    // Navigate to the module editor with the specific module ID
    router.push(`/module-editor/${moduleId}`);
  };

  return (
    <div className="h-full">
      <AssetModuleDashboard
        onCreateModule={handleCreateModule}
        onEditModule={handleEditModule}
      />
    </div>
  );
}
