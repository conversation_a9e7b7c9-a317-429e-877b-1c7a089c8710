"use client";

import React, { useState, useEffect } from "react";
import { experimental_useObject as useObject } from '@ai-sdk/react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { 
  BarChart3, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Lightbulb,
  Zap,
  Target,
  Loader2,
  RefreshCw,
  Download,
  Share2,
  Eye,
  AlertCircle,
  Info,
  Star
} from "lucide-react";
import { AssetModule } from "@/lib/types/asset-modules";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { z } from 'zod';

// Schema for AI module analysis
const ModuleAnalysisSchema = z.object({
  analysis: z.object({
    complexity: z.enum(['low', 'medium', 'high']),
    completeness: z.number().min(0).max(100),
    qualityScore: z.number().min(0).max(100),
    issues: z.array(z.object({
      type: z.enum(['error', 'warning', 'suggestion']),
      message: z.string(),
      field: z.string().optional(),
      severity: z.enum(['low', 'medium', 'high']),
      category: z.string()
    })),
    suggestions: z.array(z.object({
      type: z.enum(['field', 'logic', 'validation', 'rendering', 'performance']),
      title: z.string(),
      description: z.string(),
      impact: z.enum(['low', 'medium', 'high']),
      effort: z.enum(['low', 'medium', 'high'])
    })),
    strengths: z.array(z.string()),
    weaknesses: z.array(z.string())
  }),
  recommendations: z.array(z.object({
    priority: z.enum(['low', 'medium', 'high']),
    title: z.string(),
    description: z.string(),
    category: z.string(),
    estimatedImpact: z.string()
  })),
  benchmarks: z.object({
    industryAverage: z.number(),
    bestPracticeScore: z.number(),
    userExperienceScore: z.number(),
    maintainabilityScore: z.number()
  })
});

interface AIModuleAnalysisProps {
  module: AssetModule;
  onRecommendationApply?: (recommendation: any) => void;
  className?: string;
}

export function AIModuleAnalysis({ 
  module, 
  onRecommendationApply, 
  className = "" 
}: AIModuleAnalysisProps) {
  const [analysisHistory, setAnalysisHistory] = useState<any[]>([]);
  const [autoAnalyze, setAutoAnalyze] = useState(false);

  const { object, submit, isLoading, error, stop } = useObject({
    api: '/api/ai/module-analysis',
    schema: ModuleAnalysisSchema,
    onFinish: ({ object, error }) => {
      if (object) {
        setAnalysisHistory(prev => [{
          timestamp: new Date(),
          analysis: object,
          moduleVersion: module.version
        }, ...prev.slice(0, 4)]); // Keep last 5 analyses
      }
    }
  });

  // Auto-analyze when module changes (if enabled)
  useEffect(() => {
    if (autoAnalyze && module) {
      handleAnalyze();
    }
  }, [module.updatedAt, autoAnalyze]);

  const handleAnalyze = () => {
    submit({
      moduleData: JSON.stringify(module),
      analysisType: 'comprehensive',
      includeComparison: analysisHistory.length > 0
    });
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBackground = (score: number) => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getImpactIcon = (impact: string) => {
    switch (impact) {
      case 'high': return TrendingUp;
      case 'medium': return Target;
      case 'low': return Info;
      default: return Info;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="p-2 rounded-full bg-gradient-to-r from-blue-500 to-purple-600">
                <BarChart3 className="h-4 w-4 text-white" />
              </div>
              <div>
                <CardTitle className="text-lg">AI Module Analysis</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Comprehensive analysis of {module.name}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setAutoAnalyze(!autoAnalyze)}
                    className={autoAnalyze ? 'bg-primary/10' : ''}
                  >
                    <Zap className="h-4 w-4" />
                    Auto
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  {autoAnalyze ? 'Disable' : 'Enable'} auto-analysis
                </TooltipContent>
              </Tooltip>

              <Button
                onClick={handleAnalyze}
                disabled={isLoading}
                size="sm"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
                Analyze
              </Button>
            </div>
          </div>
        </CardHeader>

        {isLoading && (
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Analyzing module structure and quality...</span>
              </div>
              <Progress value={65} className="h-2" />
            </div>
          </CardContent>
        )}

        {error && (
          <CardContent>
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Failed to analyze module. Please try again.
              </AlertDescription>
            </Alert>
          </CardContent>
        )}
      </Card>

      {/* Analysis Results */}
      {object && (
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="issues">Issues</TabsTrigger>
            <TabsTrigger value="suggestions">Suggestions</TabsTrigger>
            <TabsTrigger value="benchmarks">Benchmarks</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Quality Score */}
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Quality Score</p>
                      <p className={`text-2xl font-bold ${getScoreColor(object.analysis.qualityScore)}`}>
                        {object.analysis.qualityScore}/100
                      </p>
                    </div>
                    <div className={`p-3 rounded-full ${getScoreBackground(object.analysis.qualityScore)}`}>
                      <Star className={`h-6 w-6 ${getScoreColor(object.analysis.qualityScore)}`} />
                    </div>
                  </div>
                  <Progress 
                    value={object.analysis.qualityScore} 
                    className="mt-3 h-2" 
                  />
                </CardContent>
              </Card>

              {/* Completeness */}
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Completeness</p>
                      <p className={`text-2xl font-bold ${getScoreColor(object.analysis.completeness)}`}>
                        {object.analysis.completeness}%
                      </p>
                    </div>
                    <div className={`p-3 rounded-full ${getScoreBackground(object.analysis.completeness)}`}>
                      <CheckCircle className={`h-6 w-6 ${getScoreColor(object.analysis.completeness)}`} />
                    </div>
                  </div>
                  <Progress 
                    value={object.analysis.completeness} 
                    className="mt-3 h-2" 
                  />
                </CardContent>
              </Card>

              {/* Complexity */}
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Complexity</p>
                      <p className="text-2xl font-bold capitalize">
                        {object.analysis.complexity}
                      </p>
                    </div>
                    <div className="p-3 rounded-full bg-blue-100">
                      <BarChart3 className="h-6 w-6 text-blue-600" />
                    </div>
                  </div>
                  <Badge 
                    variant="outline" 
                    className={`mt-3 ${
                      object.analysis.complexity === 'high' ? 'border-red-200 text-red-600' :
                      object.analysis.complexity === 'medium' ? 'border-yellow-200 text-yellow-600' :
                      'border-green-200 text-green-600'
                    }`}
                  >
                    {object.analysis.complexity} complexity
                  </Badge>
                </CardContent>
              </Card>
            </div>

            {/* Strengths and Weaknesses */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    Strengths
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {object.analysis.strengths.map((strength, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{strength}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-600" />
                    Areas for Improvement
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {object.analysis.weaknesses.map((weakness, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{weakness}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Issues Tab */}
          <TabsContent value="issues" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Identified Issues ({object.analysis.issues.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-96">
                  <div className="space-y-3">
                    {object.analysis.issues.map((issue, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge className={getSeverityColor(issue.severity)}>
                                {issue.severity}
                              </Badge>
                              <Badge variant="outline">
                                {issue.type}
                              </Badge>
                              <Badge variant="secondary">
                                {issue.category}
                              </Badge>
                            </div>
                            <p className="text-sm font-medium mb-1">{issue.message}</p>
                            {issue.field && (
                              <p className="text-xs text-muted-foreground">
                                Field: {issue.field}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Suggestions Tab */}
          <TabsContent value="suggestions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>AI Suggestions ({object.analysis.suggestions.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-96">
                  <div className="space-y-3">
                    {object.analysis.suggestions.map((suggestion, index) => {
                      const ImpactIcon = getImpactIcon(suggestion.impact);
                      return (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <ImpactIcon className="h-4 w-4" />
                                <h4 className="font-medium">{suggestion.title}</h4>
                                <Badge variant="outline">
                                  {suggestion.type}
                                </Badge>
                              </div>
                              <p className="text-sm text-muted-foreground mb-2">
                                {suggestion.description}
                              </p>
                              <div className="flex items-center gap-2">
                                <Badge className={getSeverityColor(suggestion.impact)}>
                                  {suggestion.impact} impact
                                </Badge>
                                <Badge variant="secondary">
                                  {suggestion.effort} effort
                                </Badge>
                              </div>
                            </div>
                            {onRecommendationApply && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => onRecommendationApply(suggestion)}
                              >
                                Apply
                              </Button>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Benchmarks Tab */}
          <TabsContent value="benchmarks" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(object.benchmarks).map(([key, value]) => (
                <Card key={key}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </p>
                        <p className={`text-2xl font-bold ${getScoreColor(value as number)}`}>
                          {value}/100
                        </p>
                      </div>
                      <div className={`p-3 rounded-full ${getScoreBackground(value as number)}`}>
                        <Target className={`h-6 w-6 ${getScoreColor(value as number)}`} />
                      </div>
                    </div>
                    <Progress 
                      value={value as number} 
                      className="mt-3 h-2" 
                    />
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
