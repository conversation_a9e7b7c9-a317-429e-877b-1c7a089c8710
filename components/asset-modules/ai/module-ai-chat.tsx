"use client";

import React, { useState, useRef, useEffect } from "react";
import { useChat } from '@ai-sdk/react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Bot, 
  User, 
  Send, 
  Loader2, 
  Sparkles, 
  MessageSquare,
  Lightbulb,
  Zap,
  CheckCircle,
  AlertCircle,
  Copy,
  ThumbsUp,
  ThumbsDown,
  RefreshCw,
  Minimize2,
  Maximize2
} from "lucide-react";
import { AssetModule } from "@/lib/types/asset-modules";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";

interface ModuleAIChatProps {
  module: AssetModule;
  onModuleUpdate?: (updates: Partial<AssetModule>) => void;
  isMinimized?: boolean;
  onToggleMinimize?: () => void;
  className?: string;
}

interface AIMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  type?: 'suggestion' | 'analysis' | 'error' | 'success';
  metadata?: {
    toolCalls?: any[];
    confidence?: number;
    category?: string;
  };
}

export function ModuleAIChat({ 
  module, 
  onModuleUpdate, 
  isMinimized = false,
  onToggleMinimize,
  className = "" 
}: ModuleAIChatProps) {
  const [isExpanded, setIsExpanded] = useState(!isMinimized);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  
  const { messages, input, handleInputChange, handleSubmit, isLoading, error, reload } = useChat({
    api: '/api/ai/module-chat',
    body: {
      moduleId: module.id,
      moduleData: JSON.stringify(module)
    },
    onFinish: (message) => {
      // Handle AI responses and potential module updates
      handleAIResponse(message);
    },
    onError: (error) => {
      console.error('AI Chat Error:', error);
    }
  });

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const handleAIResponse = (message: any) => {
    // Parse AI response for actionable suggestions
    try {
      if (message.content.includes('SUGGESTION:') && onModuleUpdate) {
        // Extract and apply suggestions
        const suggestionMatch = message.content.match(/SUGGESTION:\s*({.*})/s);
        if (suggestionMatch) {
          const suggestion = JSON.parse(suggestionMatch[1]);
          onModuleUpdate(suggestion);
        }
      }
    } catch (error) {
      console.error('Error parsing AI suggestion:', error);
    }
  };

  const getMessageIcon = (role: string, type?: string) => {
    if (role === 'user') return User;
    
    switch (type) {
      case 'suggestion': return Lightbulb;
      case 'analysis': return Zap;
      case 'error': return AlertCircle;
      case 'success': return CheckCircle;
      default: return Bot;
    }
  };

  const getMessageColor = (role: string, type?: string) => {
    if (role === 'user') return 'text-blue-600';
    
    switch (type) {
      case 'suggestion': return 'text-yellow-600';
      case 'analysis': return 'text-purple-600';
      case 'error': return 'text-red-600';
      case 'success': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const quickPrompts = [
    {
      label: "Suggest Fields",
      prompt: `Suggest appropriate fields for this ${module.category} module: "${module.description}"`
    },
    {
      label: "Analyze Module",
      prompt: "Analyze this module for completeness and suggest improvements"
    },
    {
      label: "Generate Logic",
      prompt: "Generate business logic flows for this module based on its fields"
    },
    {
      label: "Optimize Performance",
      prompt: "How can I optimize this module for better performance and user experience?"
    },
    {
      label: "Validate Fields",
      prompt: "Validate the current field configuration and suggest improvements"
    }
  ];

  if (isMinimized) {
    return (
      <div className={`fixed bottom-4 right-4 z-50 ${className}`}>
        <Button
          onClick={onToggleMinimize}
          className="rounded-full w-12 h-12 shadow-lg bg-primary hover:bg-primary/90"
        >
          <MessageSquare className="h-5 w-5" />
        </Button>
      </div>
    );
  }

  return (
    <Card className={`flex flex-col h-full ${className}`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="flex items-center gap-2">
          <div className="p-2 rounded-full bg-gradient-to-r from-blue-500 to-purple-600">
            <Sparkles className="h-4 w-4 text-white" />
          </div>
          <div>
            <CardTitle className="text-lg">AI Assistant</CardTitle>
            <p className="text-sm text-muted-foreground">
              Module: {module.name}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-1">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => reload()}
                disabled={isLoading}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Restart Conversation</TooltipContent>
          </Tooltip>
          {onToggleMinimize && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onToggleMinimize}
                >
                  <Minimize2 className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Minimize</TooltipContent>
            </Tooltip>
          )}
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {/* Quick Prompts */}
        <div className="p-4 border-b">
          <p className="text-sm font-medium mb-2">Quick Actions:</p>
          <div className="flex flex-wrap gap-2">
            {quickPrompts.map((prompt, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => {
                  const event = new Event('submit') as any;
                  event.preventDefault = () => {};
                  handleSubmit(event, {
                    body: {
                      moduleId: module.id,
                      moduleData: JSON.stringify(module)
                    }
                  });
                  // Set input value
                  const inputElement = document.querySelector('input[name="message"]') as HTMLInputElement;
                  if (inputElement) {
                    inputElement.value = prompt.prompt;
                    handleInputChange({ target: { value: prompt.prompt } } as any);
                  }
                }}
                className="text-xs"
                disabled={isLoading}
              >
                {prompt.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Messages */}
        <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
          <div className="space-y-4">
            {messages.length === 0 && (
              <div className="text-center py-8">
                <Bot className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">AI Assistant Ready</h3>
                <p className="text-muted-foreground">
                  Ask me anything about your module design, validation, or optimization.
                </p>
              </div>
            )}

            {messages.map((message) => {
              const MessageIcon = getMessageIcon(message.role);
              const messageColor = getMessageColor(message.role);

              return (
                <div
                  key={message.id}
                  className={`flex gap-3 ${
                    message.role === 'user' ? 'justify-end' : 'justify-start'
                  }`}
                >
                  {message.role === 'assistant' && (
                    <div className={`p-2 rounded-full bg-muted ${messageColor}`}>
                      <MessageIcon className="h-4 w-4" />
                    </div>
                  )}
                  
                  <div
                    className={`max-w-[80%] rounded-lg p-3 ${
                      message.role === 'user'
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted'
                    }`}
                  >
                    <div className="prose prose-sm max-w-none">
                      {message.content}
                    </div>
                    
                    <div className="flex items-center justify-between mt-2 pt-2 border-t border-border/50">
                      <span className="text-xs text-muted-foreground">
                        {new Date().toLocaleTimeString()}
                      </span>
                      <div className="flex items-center gap-1">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(message.content)}
                              className="h-6 w-6 p-0"
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Copy Message</TooltipContent>
                        </Tooltip>
                        
                        {message.role === 'assistant' && (
                          <>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0 text-green-600"
                                >
                                  <ThumbsUp className="h-3 w-3" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>Helpful</TooltipContent>
                            </Tooltip>
                            
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0 text-red-600"
                                >
                                  <ThumbsDown className="h-3 w-3" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>Not Helpful</TooltipContent>
                            </Tooltip>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  {message.role === 'user' && (
                    <div className="p-2 rounded-full bg-primary text-primary-foreground">
                      <User className="h-4 w-4" />
                    </div>
                  )}
                </div>
              );
            })}

            {isLoading && (
              <div className="flex gap-3 justify-start">
                <div className="p-2 rounded-full bg-muted">
                  <Loader2 className="h-4 w-4 animate-spin" />
                </div>
                <div className="bg-muted rounded-lg p-3">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm">AI is thinking...</span>
                  </div>
                </div>
              </div>
            )}

            {error && (
              <div className="flex gap-3 justify-start">
                <div className="p-2 rounded-full bg-red-100 text-red-600">
                  <AlertCircle className="h-4 w-4" />
                </div>
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-sm text-red-600">
                    Something went wrong. Please try again.
                  </p>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Input */}
        <div className="p-4 border-t">
          <form onSubmit={handleSubmit} className="flex gap-2">
            <Input
              name="message"
              value={input}
              onChange={handleInputChange}
              placeholder="Ask about your module..."
              disabled={isLoading}
              className="flex-1"
            />
            <Button type="submit" disabled={isLoading || !input.trim()}>
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </form>
        </div>
      </CardContent>
    </Card>
  );
}
