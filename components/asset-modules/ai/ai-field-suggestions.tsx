"use client";

import React, { useState } from "react";
import { experimental_useObject as useObject } from '@ai-sdk/react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Sparkles, 
  Plus, 
  Check, 
  X, 
  Loader2, 
  Lightbulb,
  AlertCircle,
  Info,
  RefreshCw,
  Wand2
} from "lucide-react";
import { AssetModule, ModuleField } from "@/lib/types/asset-modules";
import { Toolt<PERSON>, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { z } from 'zod';

// Schema for AI field suggestions
const FieldSuggestionsSchema = z.object({
  suggestions: z.array(z.object({
    name: z.string(),
    type: z.enum(['text', 'number', 'date', 'boolean', 'select', 'multiselect', 'file', 'url']),
    label: z.string(),
    description: z.string(),
    required: z.boolean(),
    validation: z.object({
      min: z.number().optional(),
      max: z.number().optional(),
      pattern: z.string().optional(),
      options: z.array(z.string()).optional()
    }).optional(),
    reasoning: z.string(),
    confidence: z.number().min(0).max(1)
  })),
  overallConfidence: z.number().min(0).max(1),
  explanation: z.string()
});

interface AIFieldSuggestionsProps {
  module: AssetModule;
  onFieldsAdd: (fields: ModuleField[]) => void;
  className?: string;
}

interface SuggestedField {
  name: string;
  type: string;
  label: string;
  description: string;
  required: boolean;
  validation?: any;
  reasoning: string;
  confidence: number;
  selected?: boolean;
}

export function AIFieldSuggestions({ 
  module, 
  onFieldsAdd, 
  className = "" 
}: AIFieldSuggestionsProps) {
  const [selectedFields, setSelectedFields] = useState<Set<string>>(new Set());
  const [isGenerating, setIsGenerating] = useState(false);

  const { object, submit, isLoading, error, stop } = useObject({
    api: '/api/ai/field-suggestions',
    schema: FieldSuggestionsSchema,
    onFinish: ({ object, error }) => {
      setIsGenerating(false);
      if (object) {
        // Auto-select high-confidence suggestions
        const highConfidenceFields = new Set(
          object.suggestions
            .filter(field => field.confidence > 0.8)
            .map(field => field.name)
        );
        setSelectedFields(highConfidenceFields);
      }
    },
    onError: (error) => {
      setIsGenerating(false);
      console.error('Field suggestion error:', error);
    }
  });

  const handleGenerateSuggestions = () => {
    setIsGenerating(true);
    setSelectedFields(new Set());
    
    submit({
      description: module.description,
      category: module.category,
      existingFields: module.fields.map(f => f.name),
      context: `Module: ${module.name}\nVersion: ${module.version}\nAuthor: ${module.author}`
    });
  };

  const toggleFieldSelection = (fieldName: string) => {
    const newSelection = new Set(selectedFields);
    if (newSelection.has(fieldName)) {
      newSelection.delete(fieldName);
    } else {
      newSelection.add(fieldName);
    }
    setSelectedFields(newSelection);
  };

  const handleAddSelectedFields = () => {
    if (!object?.suggestions) return;

    const fieldsToAdd: ModuleField[] = object.suggestions
      .filter(suggestion => selectedFields.has(suggestion.name))
      .map(suggestion => ({
        id: suggestion.name.toLowerCase().replace(/\s+/g, '-'),
        name: suggestion.name,
        type: suggestion.type as any,
        label: suggestion.label,
        description: suggestion.description,
        required: suggestion.required,
        validation: suggestion.validation || {},
        placeholder: `Enter ${suggestion.label.toLowerCase()}...`,
        helpText: suggestion.reasoning
      }));

    onFieldsAdd(fieldsToAdd);
    setSelectedFields(new Set());
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-100';
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 0.8) return 'High';
    if (confidence >= 0.6) return 'Medium';
    return 'Low';
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="p-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-600">
              <Sparkles className="h-4 w-4 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg">AI Field Suggestions</CardTitle>
              <p className="text-sm text-muted-foreground">
                Intelligent field recommendations for your module
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleGenerateSuggestions}
                  disabled={isLoading || isGenerating}
                >
                  {isLoading || isGenerating ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Wand2 className="h-4 w-4" />
                  )}
                  Generate
                </Button>
              </TooltipTrigger>
              <TooltipContent>Generate AI field suggestions</TooltipContent>
            </Tooltip>

            {(isLoading || isGenerating) && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={stop}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Stop generation</TooltipContent>
              </Tooltip>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Loading State */}
        {(isLoading || isGenerating) && (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm">Analyzing module and generating field suggestions...</span>
            </div>
            <Progress value={isGenerating ? 75 : 25} className="h-2" />
          </div>
        )}

        {/* Error State */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to generate field suggestions. Please try again.
            </AlertDescription>
          </Alert>
        )}

        {/* No Suggestions State */}
        {!object && !isLoading && !isGenerating && !error && (
          <div className="text-center py-8">
            <Lightbulb className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Ready to Suggest Fields</h3>
            <p className="text-muted-foreground mb-4">
              Click "Generate" to get AI-powered field suggestions for your {module.category} module.
            </p>
            <Button onClick={handleGenerateSuggestions}>
              <Wand2 className="h-4 w-4 mr-2" />
              Generate Suggestions
            </Button>
          </div>
        )}

        {/* Suggestions */}
        {object && (
          <div className="space-y-4">
            {/* Overall Analysis */}
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">AI Analysis</span>
                    <Badge className={getConfidenceColor(object.overallConfidence)}>
                      {getConfidenceLabel(object.overallConfidence)} Confidence
                    </Badge>
                  </div>
                  <p className="text-sm">{object.explanation}</p>
                </div>
              </AlertDescription>
            </Alert>

            {/* Field Suggestions */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Suggested Fields ({object.suggestions.length})</h4>
                {selectedFields.size > 0 && (
                  <Button
                    onClick={handleAddSelectedFields}
                    size="sm"
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Selected ({selectedFields.size})
                  </Button>
                )}
              </div>

              <ScrollArea className="h-96">
                <div className="space-y-3">
                  {object.suggestions.map((suggestion, index) => (
                    <Card 
                      key={index}
                      className={`cursor-pointer transition-all ${
                        selectedFields.has(suggestion.name)
                          ? 'ring-2 ring-primary bg-primary/5'
                          : 'hover:bg-muted/50'
                      }`}
                      onClick={() => toggleFieldSelection(suggestion.name)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 space-y-2">
                            <div className="flex items-center gap-2">
                              <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                                selectedFields.has(suggestion.name)
                                  ? 'bg-primary border-primary'
                                  : 'border-muted-foreground'
                              }`}>
                                {selectedFields.has(suggestion.name) && (
                                  <Check className="h-3 w-3 text-primary-foreground" />
                                )}
                              </div>
                              <h5 className="font-medium">{suggestion.label}</h5>
                              <Badge variant="outline" className="text-xs">
                                {suggestion.type}
                              </Badge>
                              {suggestion.required && (
                                <Badge variant="destructive" className="text-xs">
                                  Required
                                </Badge>
                              )}
                            </div>
                            
                            <p className="text-sm text-muted-foreground">
                              {suggestion.description}
                            </p>
                            
                            <div className="text-xs text-muted-foreground bg-muted p-2 rounded">
                              <strong>AI Reasoning:</strong> {suggestion.reasoning}
                            </div>

                            {suggestion.validation && Object.keys(suggestion.validation).length > 0 && (
                              <div className="text-xs">
                                <strong>Validation:</strong>
                                <ul className="list-disc list-inside ml-2 text-muted-foreground">
                                  {suggestion.validation.min && (
                                    <li>Minimum: {suggestion.validation.min}</li>
                                  )}
                                  {suggestion.validation.max && (
                                    <li>Maximum: {suggestion.validation.max}</li>
                                  )}
                                  {suggestion.validation.pattern && (
                                    <li>Pattern: {suggestion.validation.pattern}</li>
                                  )}
                                  {suggestion.validation.options && (
                                    <li>Options: {suggestion.validation.options.join(', ')}</li>
                                  )}
                                </ul>
                              </div>
                            )}
                          </div>
                          
                          <div className="ml-4">
                            <Badge className={getConfidenceColor(suggestion.confidence)}>
                              {Math.round(suggestion.confidence * 100)}%
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-between pt-4 border-t">
              <div className="text-sm text-muted-foreground">
                Select fields to add to your module
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedFields(new Set())}
                  disabled={selectedFields.size === 0}
                >
                  Clear Selection
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedFields(new Set(object.suggestions.map(s => s.name)))}
                >
                  Select All
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
