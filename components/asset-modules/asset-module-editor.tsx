
"use client";

import React, { useState, useCallback, useEffect, useRef } from "react";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  // Core editor icons
  Save,
  X,
  Download,
  Play,
  Settings,
  // Panel controls
  ChevronLeft,
  ChevronRight,
  PanelLeftOpen,
  PanelRightOpen,
  PanelBottomOpen,
  // Navigation and structure
  Folder,
  Eye,
  Plus,
  // Editor functionality
  Layers,
  Workflow,
  Palette,
  Shield,
  Search,
  Command as CommandIcon,
  Copy,
  // Device preview
  Monitor,
  Smartphone,
  Tablet,
  // Zoom and view controls
  ZoomIn,
  ZoomOut,
  RefreshCw,
  // Status indicators
  AlertCircle,
  CheckCircle,
  Clock,
  // AI icons
  Sparkles,
  MessageSquare,
  RotateCcw,
  RotateCw,
  Terminal,
  Bug,
  Maximize
} from "lucide-react";
import { AssetModule } from "@/lib/types/asset-modules";
// Reuse existing components
import { ModuleFieldDesigner } from "./module-field-designer";
import { ModuleLogicEditor } from "./module-logic-editor";
import { ModuleRenderingEditor } from "./module-rendering-editor";
import { ModuleValidationEditor } from "./module-validation-editor";
import { ModulePreview } from "./module-preview";

// New IDE-specific components
import { ModuleStructureTree } from "./editor/module-structure-tree";
import { ModulePropertiesPanel } from "./editor/module-properties-panel";
import { ModuleConsolePanel } from "./editor/module-console-panel";
import { ModuleEditorHeader } from "./editor/module-editor-header";
import { ModuleEditorTabs } from "./editor/module-editor-tabs";
import { useModuleEditorState, EditorTab } from "./editor/use-module-editor-state";
// AI Components
import { ModuleAIChat } from "./ai/module-ai-chat";
import { AIFieldSuggestions } from "./ai/ai-field-suggestions";
import { AIModuleAnalysis } from "./ai/ai-module-analysis";

interface AssetModuleEditorProps {
  module: AssetModule;
  onUpdate: (updates: Partial<AssetModule>) => void;
  onSave: (module: AssetModule) => Promise<AssetModule>;
  onClose: () => void;
  onExport: (module: AssetModule) => void;
  hasUnsavedChanges: boolean;
  isNewModule?: boolean;
}

export function AssetModuleEditor({
  module,
  onUpdate,
  onSave,
  onClose,
  onExport,
  hasUnsavedChanges,
  isNewModule = false,
}: AssetModuleEditorProps) {
  const {
    selectedItem,
    setSelectedItem,
    activeTab,
    setActiveTab,
    openTabs,
    setOpenTabs,
    isSaving,
    setIsSaving,
    saveStatus,
    setSaveStatus,
    showConsole,
    setShowConsole,
    consoleOutput,
    setConsoleOutput,
  } = useModuleEditorState();

  // Enhanced UI State
  const [leftPanelCollapsed, setLeftPanelCollapsed] = useState(false);
  const [rightPanelCollapsed, setRightPanelCollapsed] = useState(false);
  const [bottomPanelCollapsed, setBottomPanelCollapsed] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showCommandPalette, setShowCommandPalette] = useState(false);
  const [showSearchDialog, setShowSearchDialog] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [zoomLevel, setZoomLevel] = useState(100);
  const [showMinimap, setShowMinimap] = useState(false);
  const [showBreadcrumbs, setShowBreadcrumbs] = useState(true);
  const [showLineNumbers, setShowLineNumbers] = useState(true);
  const [theme, setTheme] = useState<'light' | 'dark' | 'auto'>('auto');
  const [layoutMode, setLayoutMode] = useState<'horizontal' | 'vertical'>('horizontal');

  // Advanced Features State
  const [undoStack, setUndoStack] = useState<AssetModule[]>([]);
  const [redoStack, setRedoStack] = useState<AssetModule[]>([]);
  const [isDebugging, setIsDebugging] = useState(false);
  const [breakpoints, setBreakpoints] = useState<Set<string>>(new Set());
  const [watchedVariables, setWatchedVariables] = useState<string[]>([]);
  const [collaborators, setCollaborators] = useState<Array<{id: string, name: string, cursor?: {x: number, y: number}}>>([]);
  const [showCollaborators, setShowCollaborators] = useState(true);

  // AI Features State
  const [showAIChat, setShowAIChat] = useState(false);
  const [aiChatMinimized, setAIChatMinimized] = useState(true);
  const [showAIPanel, setShowAIPanel] = useState(false);
  const [aiPanelTab, setAIPanelTab] = useState<'suggestions' | 'analysis' | 'chat'>('suggestions');

  // Refs for advanced functionality
  const editorRef = useRef<HTMLDivElement>(null);
  const commandPaletteRef = useRef<HTMLInputElement>(null);

  // Auto-save functionality
  useEffect(() => {
    if (!hasUnsavedChanges) return;

    const autoSaveTimer = setTimeout(() => {
      handleAutoSave();
    }, 30000); // Auto-save after 30 seconds

    return () => clearTimeout(autoSaveTimer);
  }, [hasUnsavedChanges, module]);

  const handleAutoSave = async () => {
    if (!hasUnsavedChanges) return;

    try {
      setSaveStatus('saving');
      await onSave(module);
      setSaveStatus('saved');
      
      // Reset status after 2 seconds
      setTimeout(() => setSaveStatus('idle'), 2000);
    } catch (error) {
      setSaveStatus('error');
      console.error('Auto-save failed:', error);
      // Reset status after 3 seconds
      setTimeout(() => setSaveStatus('idle'), 3000);
    }
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      setSaveStatus('saving');
      
      await onSave(module);
      setSaveStatus('saved');
      
      setConsoleOutput(prev => [...prev, {
        type: 'success',
        message: 'Module saved successfully',
        timestamp: new Date(),
      }]);

      // Reset status after 2 seconds
      setTimeout(() => setSaveStatus('idle'), 2000);
    } catch (error) {
      setSaveStatus('error');
      setConsoleOutput(prev => [...prev, {
        type: 'error',
        message: 'Failed to save module',
        timestamp: new Date(),
      }]);
      console.error('Save failed:', error);
      // Reset status after 3 seconds
      setTimeout(() => setSaveStatus('idle'), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  const handleTest = () => {
    setConsoleOutput(prev => [...prev, {
      type: 'info',
      message: 'Running module tests...',
      timestamp: new Date(),
    }]);

    // Simulate testing
    setTimeout(() => {
      setConsoleOutput(prev => [...prev, {
        type: 'success',
        message: 'All tests passed',
        timestamp: new Date(),
      }]);
    }, 1000);

    setShowConsole(true);
  };

  const handleExport = () => {
    onExport(module);
    setConsoleOutput(prev => [...prev, {
      type: 'info',
      message: 'Module exported successfully',
      timestamp: new Date(),
    }]);
  };

  // AI Feature Handlers
  const handleAIFieldsAdd = (fields: any[]) => {
    const updatedModule = {
      ...module,
      fields: [...module.fields, ...fields]
    };
    onUpdate(updatedModule);
    setConsoleOutput(prev => [...prev, {
      type: 'success',
      message: `Added ${fields.length} AI-suggested fields`,
      timestamp: new Date(),
    }]);
  };

  const handleAIRecommendationApply = (recommendation: any) => {
    setConsoleOutput(prev => [...prev, {
      type: 'info',
      message: `Applying AI recommendation: ${recommendation.title}`,
      timestamp: new Date(),
    }]);
    // Implementation would depend on recommendation type
  };

  const toggleAIChat = () => {
    setShowAIChat(!showAIChat);
    setAIChatMinimized(false);
  };

  const toggleAIPanel = () => {
    setShowAIPanel(!showAIPanel);
  };

  const handleTabClose = (tabId: string) => {
    const newTabs = openTabs.filter(tab => tab.id !== tabId);
    setOpenTabs(newTabs);
    
    if (activeTab === tabId && newTabs.length > 0) {
      setActiveTab(newTabs[0].id);
    }
  };

  const handleTabAdd = (tab: EditorTab) => {
    if (!openTabs.find(t => t.id === tab.id)) {
      setOpenTabs([...openTabs, tab]);
    }
    setActiveTab(tab.id);
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // Advanced Editor Functions
  const handleUndo = useCallback(() => {
    if (undoStack.length > 0) {
      const previousState = undoStack[undoStack.length - 1];
      setRedoStack(prev => [...prev, module]);
      setUndoStack(prev => prev.slice(0, -1));
      onUpdate(previousState);

      setConsoleOutput(prev => [...prev, {
        type: 'info',
        message: 'Undo applied',
        timestamp: new Date(),
      }]);
    }
  }, [undoStack, module, onUpdate]);

  const handleRedo = useCallback(() => {
    if (redoStack.length > 0) {
      const nextState = redoStack[redoStack.length - 1];
      setUndoStack(prev => [...prev, module]);
      setRedoStack(prev => prev.slice(0, -1));
      onUpdate(nextState);

      setConsoleOutput(prev => [...prev, {
        type: 'info',
        message: 'Redo applied',
        timestamp: new Date(),
      }]);
    }
  }, [redoStack, module, onUpdate]);

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    // TODO: Implement search functionality across module content
    setConsoleOutput(prev => [...prev, {
      type: 'info',
      message: `Searching for: ${query}`,
      timestamp: new Date(),
    }]);
  }, []);

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 10, 200));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 10, 50));
  };

  const handleResetZoom = () => {
    setZoomLevel(100);
  };

  const toggleDebugMode = () => {
    setIsDebugging(!isDebugging);
    setConsoleOutput(prev => [...prev, {
      type: 'info',
      message: `Debug mode ${!isDebugging ? 'enabled' : 'disabled'}`,
      timestamp: new Date(),
    }]);
  };

  const handleAddBreakpoint = (id: string) => {
    setBreakpoints(prev => new Set([...prev, id]));
  };

  const handleRemoveBreakpoint = (id: string) => {
    setBreakpoints(prev => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  };

  const handleCommandPalette = () => {
    setShowCommandPalette(true);
    setTimeout(() => {
      commandPaletteRef.current?.focus();
    }, 100);
  };

  const executeCommand = (command: string) => {
    setShowCommandPalette(false);

    switch (command) {
      case 'save':
        handleSave();
        break;
      case 'test':
        handleTest();
        break;
      case 'export':
        handleExport();
        break;
      case 'undo':
        handleUndo();
        break;
      case 'redo':
        handleRedo();
        break;
      case 'search':
        setShowSearchDialog(true);
        break;
      case 'toggle-console':
        setShowConsole(!showConsole);
        break;
      case 'toggle-debug':
        toggleDebugMode();
        break;
      case 'zoom-in':
        handleZoomIn();
        break;
      case 'zoom-out':
        handleZoomOut();
        break;
      case 'reset-zoom':
        handleResetZoom();
        break;
      case 'fullscreen':
        toggleFullscreen();
        break;
      default:
        setConsoleOutput(prev => [...prev, {
          type: 'warning',
          message: `Unknown command: ${command}`,
          timestamp: new Date(),
        }]);
    }
  };

  // Enhanced Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+S to save
      if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        handleSave();
      }

      // Ctrl+Shift+P for command palette
      if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        e.preventDefault();
        handleCommandPalette();
      }

      // Ctrl+` to toggle console
      if (e.ctrlKey && e.key === '`') {
        e.preventDefault();
        setShowConsole(!showConsole);
      }

      // Ctrl+F for search
      if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        setShowSearchDialog(true);
      }

      // Ctrl+Z for undo
      if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
        e.preventDefault();
        handleUndo();
      }

      // Ctrl+Shift+Z or Ctrl+Y for redo
      if ((e.ctrlKey && e.shiftKey && e.key === 'Z') || (e.ctrlKey && e.key === 'y')) {
        e.preventDefault();
        handleRedo();
      }

      // Ctrl+Plus for zoom in
      if (e.ctrlKey && (e.key === '+' || e.key === '=')) {
        e.preventDefault();
        handleZoomIn();
      }

      // Ctrl+Minus for zoom out
      if (e.ctrlKey && e.key === '-') {
        e.preventDefault();
        handleZoomOut();
      }

      // Ctrl+0 for reset zoom
      if (e.ctrlKey && e.key === '0') {
        e.preventDefault();
        handleResetZoom();
      }

      // F5 for test/run
      if (e.key === 'F5') {
        e.preventDefault();
        handleTest();
      }

      // F9 for toggle debug
      if (e.key === 'F9') {
        e.preventDefault();
        toggleDebugMode();
      }

      // F11 for fullscreen
      if (e.key === 'F11') {
        e.preventDefault();
        toggleFullscreen();
      }

      // Escape to close dialogs
      if (e.key === 'Escape') {
        if (showCommandPalette) {
          setShowCommandPalette(false);
        } else if (showSearchDialog) {
          setShowSearchDialog(false);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [showConsole, showCommandPalette, showSearchDialog, handleSave, handleUndo, handleRedo]);

  // Command Palette Commands
  const commands = [
    { id: 'save', label: 'Save Module', icon: Save, shortcut: 'Ctrl+S' },
    { id: 'test', label: 'Test Module', icon: Play, shortcut: 'F5' },
    { id: 'export', label: 'Export Module', icon: Download, shortcut: '' },
    { id: 'undo', label: 'Undo', icon: RotateCcw, shortcut: 'Ctrl+Z' },
    { id: 'redo', label: 'Redo', icon: RotateCw, shortcut: 'Ctrl+Y' },
    { id: 'search', label: 'Search', icon: Search, shortcut: 'Ctrl+F' },
    { id: 'toggle-console', label: 'Toggle Console', icon: Terminal, shortcut: 'Ctrl+`' },
    { id: 'toggle-debug', label: 'Toggle Debug Mode', icon: Bug, shortcut: 'F9' },
    { id: 'zoom-in', label: 'Zoom In', icon: ZoomIn, shortcut: 'Ctrl++' },
    { id: 'zoom-out', label: 'Zoom Out', icon: ZoomOut, shortcut: 'Ctrl+-' },
    { id: 'reset-zoom', label: 'Reset Zoom', icon: RefreshCw, shortcut: 'Ctrl+0' },
    { id: 'fullscreen', label: 'Toggle Fullscreen', icon: Maximize, shortcut: 'F11' },
  ];

  return (
    <TooltipProvider>
      <div
        className="h-screen flex flex-col bg-background"
        style={{ zoom: `${zoomLevel}%` }}
        ref={editorRef}
      >
        {/* Advanced Status Bar */}
        {showBreadcrumbs && (
          <div className="border-b bg-muted/30 px-4 py-1 text-xs text-muted-foreground flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span>Module</span>
              <ChevronRight className="h-3 w-3" />
              <span>{module.name}</span>
              <ChevronRight className="h-3 w-3" />
              <span className="capitalize">{activeTab}</span>
              {selectedItem.type && (
                <>
                  <ChevronRight className="h-3 w-3" />
                  <span>{selectedItem.type}: {selectedItem.id}</span>
                </>
              )}
            </div>
            <div className="flex items-center gap-4">
              <span>Zoom: {zoomLevel}%</span>
              <span>Mode: {viewMode}</span>
              {isDebugging && <Badge variant="destructive" className="text-xs">DEBUG</Badge>}
              {collaborators.length > 0 && (
                <div className="flex items-center gap-1">
                  <span>{collaborators.length} collaborator{collaborators.length > 1 ? 's' : ''}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Enhanced Editor Header */}
        <div className="border-b bg-card">
          <div className="flex items-center justify-between px-4 py-2">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <h1 className="text-lg font-semibold">{module.name}</h1>
                {hasUnsavedChanges && (
                  <Badge variant="outline" className="text-xs">
                    <Clock className="h-3 w-3 mr-1" />
                    Unsaved
                  </Badge>
                )}
                {saveStatus === 'saving' && (
                  <Badge variant="secondary" className="text-xs">
                    <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                    Saving...
                  </Badge>
                )}
                {saveStatus === 'saved' && (
                  <Badge variant="default" className="text-xs">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Saved
                  </Badge>
                )}
                {saveStatus === 'error' && (
                  <Badge variant="destructive" className="text-xs">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Error
                  </Badge>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              {/* View Mode Selector */}
              <div className="flex items-center border rounded-md">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={viewMode === 'desktop' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('desktop')}
                      className="rounded-r-none"
                    >
                      <Monitor className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Desktop View</TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={viewMode === 'tablet' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('tablet')}
                      className="rounded-none border-x"
                    >
                      <Tablet className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Tablet View</TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={viewMode === 'mobile' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('mobile')}
                      className="rounded-l-none"
                    >
                      <Smartphone className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Mobile View</TooltipContent>
                </Tooltip>
              </div>

              {/* Zoom Controls */}
              <div className="flex items-center border rounded-md">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleZoomOut}
                      disabled={zoomLevel <= 50}
                      className="rounded-r-none"
                    >
                      <ZoomOut className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Zoom Out (Ctrl+-)</TooltipContent>
                </Tooltip>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleResetZoom}
                  className="rounded-none border-x px-3 text-xs"
                >
                  {zoomLevel}%
                </Button>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleZoomIn}
                      disabled={zoomLevel >= 200}
                      className="rounded-l-none"
                    >
                      <ZoomIn className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Zoom In (Ctrl++)</TooltipContent>
                </Tooltip>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-1">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleUndo}
                      disabled={undoStack.length === 0}
                    >
                      <RotateCcw className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Undo (Ctrl+Z)</TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleRedo}
                      disabled={redoStack.length === 0}
                    >
                      <RotateCw className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Redo (Ctrl+Y)</TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowSearchDialog(true)}
                    >
                      <Search className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Search (Ctrl+F)</TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleCommandPalette}
                    >
                      <CommandIcon className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Command Palette (Ctrl+Shift+P)</TooltipContent>
                </Tooltip>
              </div>

              <Separator orientation="vertical" className="h-6" />

              <div className="flex items-center gap-1">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={isDebugging ? 'default' : 'ghost'}
                      size="sm"
                      onClick={toggleDebugMode}
                    >
                      <Bug className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Debug Mode (F9)</TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleTest}
                    >
                      <Play className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Test Module (F5)</TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleSave}
                      disabled={isSaving}
                    >
                      {isSaving ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4" />
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Save (Ctrl+S)</TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleExport}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Export Module</TooltipContent>
                </Tooltip>

                {/* AI Feature Buttons */}
                <Separator orientation="vertical" className="h-6 mx-2" />
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={showAIPanel ? "default" : "ghost"}
                      size="sm"
                      onClick={toggleAIPanel}
                      className={showAIPanel ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700" : ""}
                    >
                      <Sparkles className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>AI Tools & Analysis</TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={showAIChat ? "default" : "ghost"}
                      size="sm"
                      onClick={toggleAIChat}
                    >
                      <MessageSquare className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>AI Assistant Chat</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onClose}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Close Editor</TooltipContent>
                </Tooltip>
              </div>
            </div>
          </div>
        </div>

      {/* Main Editor Area */}
      <div className="flex-1 overflow-hidden">
        <PanelGroup direction="horizontal">
          {/* Left Panel - Enhanced Explorer */}
          {!leftPanelCollapsed && (
            <>
              <Panel defaultSize={22} minSize={18} maxSize={40}>
                <div className="h-full border-r bg-muted/30 flex flex-col">
                  {/* Explorer Header */}
                  <div className="p-3 border-b bg-background">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-semibold flex items-center gap-2">
                        <Folder className="h-4 w-4" />
                        Explorer
                      </h3>
                      <div className="flex items-center gap-1">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleTabAdd({ id: 'fields', title: 'Fields', type: 'fields' })}
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Add New Field</TooltipContent>
                        </Tooltip>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setLeftPanelCollapsed(true)}
                            >
                              <PanelLeftOpen className="h-3 w-3" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Collapse Panel</TooltipContent>
                        </Tooltip>
                      </div>
                    </div>
                  </div>

                  {/* Module Info Section */}
                  <div className="p-3 border-b bg-background/50">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-xs font-medium text-muted-foreground">MODULE</span>
                        <Badge variant="outline" className="text-xs">
                          v{module.version}
                        </Badge>
                      </div>
                      <div className="text-sm font-medium truncate">{module.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {module.fields.length} fields • {module.logic.nodes.length} nodes
                      </div>
                    </div>
                  </div>

                  {/* Enhanced Structure Tree */}
                  <div className="flex-1 overflow-hidden">
                    <ModuleStructureTree
                      module={module}
                      selectedItem={selectedItem}
                      onSelectItem={setSelectedItem}
                      onTabAdd={handleTabAdd}
                    />
                  </div>

                  {/* Quick Actions */}
                  <div className="p-2 border-t bg-background/50">
                    <div className="grid grid-cols-2 gap-1">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 text-xs"
                            onClick={() => setActiveTab('fields')}
                          >
                            <Layers className="h-3 w-3 mr-1" />
                            Fields
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Go to Fields</TooltipContent>
                      </Tooltip>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 text-xs"
                            onClick={() => setActiveTab('logic')}
                          >
                            <Workflow className="h-3 w-3 mr-1" />
                            Logic
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Go to Logic</TooltipContent>
                      </Tooltip>
                    </div>
                  </div>
                </div>
              </Panel>
              <PanelResizeHandle className="w-1 bg-border hover:bg-primary/20 transition-colors" />
            </>
          )}

          {/* Center Panel - Main Editor */}
          <Panel defaultSize={leftPanelCollapsed ? 70 : 55} minSize={30}>
            <PanelGroup direction="vertical">
              {/* Main Editing Area */}
              <Panel defaultSize={showConsole ? 70 : 100} minSize={40}>
                <div className="h-full flex flex-col">
                  {/* Collapsed Left Panel Toggle */}
                  {leftPanelCollapsed && (
                    <div className="absolute top-2 left-2 z-10">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setLeftPanelCollapsed(false)}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  )}

                  {/* Use the dedicated ModuleEditorTabs component for cleaner code */}
                  <ModuleEditorTabs
                    module={module}
                    onUpdate={onUpdate}
                    activeTab={activeTab}
                    openTabs={openTabs}
                    onTabChange={setActiveTab}
                    onTabClose={handleTabClose}
                    onTabAdd={handleTabAdd}
                    selectedItem={selectedItem}
                  />
                </div>
              </Panel>

              {/* Console Panel */}
              {showConsole && (
                <>
                  <PanelResizeHandle className="h-1 bg-border hover:bg-primary/20 transition-colors" />
                  <Panel defaultSize={30} minSize={20} maxSize={50}>
                    <ModuleConsolePanel
                      output={consoleOutput}
                      onClear={() => setConsoleOutput([])}
                      onClose={() => setShowConsole(false)}
                    />
                  </Panel>
                </>
              )}
            </PanelGroup>
          </Panel>

          {/* Right Panel - Enhanced Properties Inspector */}
          {!rightPanelCollapsed && (
            <>
              <PanelResizeHandle className="w-1 bg-border hover:bg-primary/20 transition-colors" />
              <Panel defaultSize={28} minSize={22} maxSize={45}>
                <div className="h-full border-l bg-muted/30 flex flex-col">
                  {/* Properties Header */}
                  <div className="p-3 border-b bg-background">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-semibold flex items-center gap-2">
                        {showAIPanel ? (
                          <>
                            <Sparkles className="h-4 w-4" />
                            AI Tools
                          </>
                        ) : (
                          <>
                            <Settings className="h-4 w-4" />
                            Inspector
                          </>
                        )}
                      </h3>
                      <div className="flex items-center gap-1">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setSelectedItem({ type: null, id: null })}
                            >
                              <RefreshCw className="h-3 w-3" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Reset Properties</TooltipContent>
                        </Tooltip>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setRightPanelCollapsed(true)}
                            >
                              <PanelRightOpen className="h-3 w-3" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Collapse Panel</TooltipContent>
                        </Tooltip>
                      </div>
                    </div>
                  </div>

                  {/* Selection Info */}
                  <div className="p-3 border-b bg-background/50">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-xs font-medium text-muted-foreground">SELECTED</span>
                        {selectedItem.type && (
                          <Badge variant="secondary" className="text-xs capitalize">
                            {selectedItem.type}
                          </Badge>
                        )}
                      </div>
                      <div className="text-sm font-medium truncate">
                        {selectedItem.id || 'No selection'}
                      </div>
                      {selectedItem.type && (
                        <div className="text-xs text-muted-foreground">
                          Configure properties and behavior
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Enhanced Properties Panel or AI Tools */}
                  <div className="flex-1 overflow-hidden">
                    {showAIPanel ? (
                      <div className="h-full">
                        <Tabs value={aiPanelTab} onValueChange={(value: any) => setAIPanelTab(value)} className="h-full flex flex-col">
                          <TabsList className="grid w-full grid-cols-3 m-2">
                            <TabsTrigger value="suggestions" className="text-xs">Suggestions</TabsTrigger>
                            <TabsTrigger value="analysis" className="text-xs">Analysis</TabsTrigger>
                            <TabsTrigger value="chat" className="text-xs">Chat</TabsTrigger>
                          </TabsList>

                          <TabsContent value="suggestions" className="flex-1 m-0 p-2 overflow-auto">
                            <AIFieldSuggestions
                              module={module}
                              onFieldsAdd={handleAIFieldsAdd}
                              className="h-full"
                            />
                          </TabsContent>

                          <TabsContent value="analysis" className="flex-1 m-0 p-2 overflow-auto">
                            <AIModuleAnalysis
                              module={module}
                              onRecommendationApply={handleAIRecommendationApply}
                              className="h-full"
                            />
                          </TabsContent>

                          <TabsContent value="chat" className="flex-1 m-0 p-2 overflow-auto">
                            <ModuleAIChat
                              module={module}
                              onModuleUpdate={onUpdate}
                              isMinimized={false}
                              className="h-full"
                            />
                          </TabsContent>
                        </Tabs>
                      </div>
                    ) : (
                      <ScrollArea className="h-full">
                        <div className="p-3 space-y-4">
                          {selectedItem.type ? (
                            <ModulePropertiesPanel
                              module={module}
                              selectedItem={selectedItem}
                              onUpdate={onUpdate}
                            />
                          ) : (
                            <div className="text-center py-8">
                              <div className="text-muted-foreground mb-2">
                                <Eye className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                <div className="text-sm">No item selected</div>
                                <div className="text-xs">
                                  Select a field, logic node, or component to view its properties
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </ScrollArea>
                    )}
                  </div>

                  {/* Quick Property Actions */}
                  <div className="p-2 border-t bg-background/50">
                    <div className="grid grid-cols-2 gap-1">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 text-xs"
                            disabled={!selectedItem.type}
                            onClick={() => {
                              // Copy selected item properties to clipboard
                              if (selectedItem.type && selectedItem.id) {
                                navigator.clipboard.writeText(JSON.stringify(selectedItem, null, 2));
                              }
                            }}
                          >
                            <Copy className="h-3 w-3 mr-1" />
                            Copy
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Copy Properties</TooltipContent>
                      </Tooltip>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 text-xs"
                            disabled={!selectedItem.type}
                            onClick={() => {
                              // Clear selection
                              setSelectedItem({ type: null, id: null });
                            }}
                          >
                            <X className="h-3 w-3 mr-1" />
                            Clear
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Delete Item</TooltipContent>
                      </Tooltip>
                    </div>
                  </div>
                </div>
              </Panel>
            </>
          )}

          {/* Collapsed Right Panel Toggle */}
          {rightPanelCollapsed && (
            <div className="absolute top-2 right-2 z-10">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setRightPanelCollapsed(false)}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            </div>
          )}
        </PanelGroup>
      </div>

      {/* Command Palette */}
        <Dialog open={showCommandPalette} onOpenChange={setShowCommandPalette}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Command Palette</DialogTitle>
            </DialogHeader>
            <Command>
              <CommandInput
                ref={commandPaletteRef}
                placeholder="Type a command..."
                className="border-none focus:ring-0"
              />
              <CommandList>
                <CommandEmpty>No commands found.</CommandEmpty>
                <CommandGroup heading="Actions">
                  {commands.map((command) => {
                    const IconComponent = command.icon;
                    return (
                      <CommandItem
                        key={command.id}
                        onSelect={() => executeCommand(command.id)}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center gap-2">
                          <IconComponent className="h-4 w-4" />
                          <span>{command.label}</span>
                        </div>
                        {command.shortcut && (
                          <Badge variant="outline" className="text-xs">
                            {command.shortcut}
                          </Badge>
                        )}
                      </CommandItem>
                    );
                  })}
                </CommandGroup>
              </CommandList>
            </Command>
          </DialogContent>
        </Dialog>

        {/* Search Dialog */}
        <Dialog open={showSearchDialog} onOpenChange={setShowSearchDialog}>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>Search Module</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <Input
                placeholder="Search fields, logic, validation rules..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full"
              />
              <div className="text-sm text-muted-foreground">
                Search across all module components including fields, logic nodes, validation rules, and rendering configurations.
              </div>
              {searchQuery && (
                <div className="border rounded-md p-4">
                  <div className="text-sm font-medium mb-2">Search Results</div>
                  <div className="text-sm text-muted-foreground">
                    Search functionality will be implemented to find matches across:
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>Field names and descriptions</li>
                      <li>Logic node configurations</li>
                      <li>Validation rule definitions</li>
                      <li>Rendering component settings</li>
                    </ul>
                  </div>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>

        {/* Floating AI Chat */}
        {showAIChat && (
          <div className="fixed bottom-4 right-4 w-96 h-[600px] z-50 shadow-2xl rounded-lg overflow-hidden">
            <ModuleAIChat
              module={module}
              onModuleUpdate={onUpdate}
              isMinimized={aiChatMinimized}
              onToggleMinimize={() => setAIChatMinimized(!aiChatMinimized)}
              className="h-full"
            />
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}
