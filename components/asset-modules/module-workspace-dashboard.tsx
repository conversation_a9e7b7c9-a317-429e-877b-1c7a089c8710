"use client";

import React, { useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import { 
  Plus, 
  Search, 
  Filter, 
  Grid, 
  List, 
  Star, 
  Users, 
  Calendar, 
  Package, 
  Code, 
  Layers,
  AlertCircle,
  CheckCircle,
  Clock,
  Edit,
  Copy,
  Trash2,
  Download,
  Upload,
  Eye,
  Settings,
  MoreHorizontal,
  FileText,
  BarChart3
} from "lucide-react";
import { AssetModule, ModuleCategory, ModuleTemplate } from "@/lib/types/asset-modules";
import { useAdminHeader } from "@/hooks/use-admin-header";
import { useHeaderTabs } from "@/hooks/use-admin-tabs";
import { getAssetModulesHeaderConfig } from "@/lib/utils/admin-header-configs";
import { getAssetModulesHeaderTabs } from "@/lib/utils/admin-tabs-configs";
import { HeaderTabContent } from "@/components/ui/header-tab-content";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface ModuleWorkspaceDashboardProps {
  onCreateModule?: (template?: ModuleTemplate) => void;
  onEditModule?: (moduleId: string) => void;
}

export function ModuleWorkspaceDashboard({
  onCreateModule,
  onEditModule,
}: ModuleWorkspaceDashboardProps) {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<ModuleCategory | "all">("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Mock data - in real implementation, this would come from API
  const [modules] = useState<AssetModule[]>([
    {
      id: "module-1",
      name: "Location Module",
      version: "1.2.0",
      description: "Comprehensive location tracking with address, GPS coordinates, and floor plans",
      category: "location",
      author: "System",
      tags: ["location", "gps", "address"],
      fields: [],
      logic: { nodes: [], edges: [], variables: [], functions: [] },
      rendering: { formLayout: { sections: [], columns: 1, spacing: "normal", grouping: [] }, displayLayout: { views: [], defaultView: "card" }, components: [] },
      validation: { rules: [], crossFieldValidation: [] },
      isActive: true,
      isBuiltIn: true,
      createdAt: "2024-01-15T10:00:00Z",
      updatedAt: "2024-01-20T14:30:00Z",
      usageCount: 45,
      dependencies: [],
      compatibleAssetTypes: ["equipment", "vehicle", "building"],
      requiredPermissions: ["module.read", "module.write"],
    },
    {
      id: "module-2",
      name: "Software License Module",
      version: "2.1.0",
      description: "Track software licenses, keys, and compliance information",
      category: "software",
      author: "Admin User",
      tags: ["software", "license", "compliance"],
      fields: [],
      logic: { nodes: [], edges: [], variables: [], functions: [] },
      rendering: { formLayout: { sections: [], columns: 1, spacing: "normal", grouping: [] }, displayLayout: { views: [], defaultView: "card" }, components: [] },
      validation: { rules: [], crossFieldValidation: [] },
      isActive: true,
      isBuiltIn: false,
      createdAt: "2024-02-01T09:15:00Z",
      updatedAt: "2024-02-10T16:45:00Z",
      usageCount: 23,
      dependencies: [],
      compatibleAssetTypes: ["software"],
      requiredPermissions: ["module.read"],
    },
  ]);

  const handleCreateModule = () => {
    if (onCreateModule) {
      onCreateModule();
    } else {
      router.push('/module-editor/new');
    }
  };

  const handleImportModule = () => {
    // TODO: Implement module import functionality
    console.log("Import module");
  };

  const handleExportModules = () => {
    // TODO: Implement modules export functionality
    console.log("Export modules");
  };

  // Header configuration with useMemo to prevent infinite loops
  const headerConfig = useMemo(() => 
    getAssetModulesHeaderConfig(
      handleCreateModule,
      handleImportModule,
      handleExportModules
    ), 
    []
  );

  const headerTabs = useMemo(() => getAssetModulesHeaderTabs(), []);

  useAdminHeader(headerConfig);
  useHeaderTabs(headerTabs, "workspace");

  // Function to get tab content based on tab ID
  const getTabContent = (tabId: string) => {
    switch (tabId) {
      case "workspace":
        return (
          <ModuleWorkspaceContent
            modules={filteredModules}
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            selectedCategory={selectedCategory}
            setSelectedCategory={setSelectedCategory}
            viewMode={viewMode}
            setViewMode={setViewMode}
            onEditModule={handleEditModule}
            onCreateModule={handleCreateModule}
            getCategoryIcon={getCategoryIcon}
            getCategoryColor={getCategoryColor}
          />
        );
      case "templates":
        return (
          <div className="text-center py-12">
            <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Module Templates</h3>
            <p className="text-muted-foreground mb-4">
              Pre-built module templates to get you started quickly
            </p>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Browse Templates
            </Button>
          </div>
        );
      case "marketplace":
        return (
          <div className="text-center py-12">
            <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Module Marketplace</h3>
            <p className="text-muted-foreground mb-4">
              Discover and install community-created modules
            </p>
            <Button>
              <Search className="h-4 w-4 mr-2" />
              Explore Marketplace
            </Button>
          </div>
        );
      case "analytics":
        return (
          <div className="text-center py-12">
            <BarChart3 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Module Analytics</h3>
            <p className="text-muted-foreground mb-4">
              Track module usage, performance, and adoption metrics
            </p>
            <Button>
              <Eye className="h-4 w-4 mr-2" />
              View Analytics
            </Button>
          </div>
        );
      default:
        return <div>Content for {tabId} tab</div>;
    }
  };

  const filteredModules = modules.filter(module => {
    const matchesSearch = module.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         module.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         module.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = selectedCategory === "all" || module.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleEditModule = (module: AssetModule) => {
    if (onEditModule) {
      onEditModule(module.id);
    } else {
      router.push(`/module-editor/${module.id}`);
    }
  };

  const getCategoryIcon = (category: ModuleCategory) => {
    const icons = {
      location: Layers,
      software: Code,
      hardware: Settings,
      financial: BarChart3,
      compliance: CheckCircle,
      maintenance: Settings,
      security: AlertCircle,
      custom: Package,
    };
    return icons[category] || Package;
  };

  const getCategoryColor = (category: ModuleCategory) => {
    const colors = {
      location: "bg-blue-100 text-blue-800",
      software: "bg-green-100 text-green-800",
      hardware: "bg-orange-100 text-orange-800",
      financial: "bg-purple-100 text-purple-800",
      compliance: "bg-red-100 text-red-800",
      maintenance: "bg-yellow-100 text-yellow-800",
      security: "bg-gray-100 text-gray-800",
      custom: "bg-indigo-100 text-indigo-800",
    };
    return colors[category] || "bg-gray-100 text-gray-800";
  };

  return (
    <div className="space-y-6">
      {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
          { id: "workspace", content: getTabContent("workspace") },
          { id: "templates", content: getTabContent("templates") },
          { id: "marketplace", content: getTabContent("marketplace") },
          { id: "analytics", content: getTabContent("analytics") },
        ]}
      />
    </div>
  );
}

interface ModuleWorkspaceContentProps {
  modules: AssetModule[];
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  selectedCategory: ModuleCategory | "all";
  setSelectedCategory: (category: ModuleCategory | "all") => void;
  viewMode: "grid" | "list";
  setViewMode: (mode: "grid" | "list") => void;
  onEditModule: (module: AssetModule) => void;
  onCreateModule: () => void;
  getCategoryIcon: (category: ModuleCategory) => any;
  getCategoryColor: (category: ModuleCategory) => string;
}

function ModuleWorkspaceContent({
  modules,
  searchQuery,
  setSearchQuery,
  selectedCategory,
  setSelectedCategory,
  viewMode,
  setViewMode,
  onEditModule,
  onCreateModule,
  getCategoryIcon,
  getCategoryColor,
}: ModuleWorkspaceContentProps) {
  return (
    <>
      {/* Filters and Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 gap-4 items-center">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search modules..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={selectedCategory} onValueChange={(value) => setSelectedCategory(value as ModuleCategory | "all")}>
            <SelectTrigger className="w-48">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Filter by category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="location">Location</SelectItem>
              <SelectItem value="software">Software</SelectItem>
              <SelectItem value="hardware">Hardware</SelectItem>
              <SelectItem value="financial">Financial</SelectItem>
              <SelectItem value="compliance">Compliance</SelectItem>
              <SelectItem value="maintenance">Maintenance</SelectItem>
              <SelectItem value="security">Security</SelectItem>
              <SelectItem value="custom">Custom</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === "grid" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("grid")}
          >
            <Grid className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === "list" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("list")}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Module Grid/List */}
      {modules.length === 0 ? (
        <div className="text-center py-12">
          <Layers className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No modules found</h3>
          <p className="text-muted-foreground mb-4">
            {searchQuery || selectedCategory !== "all" 
              ? "Try adjusting your search or filters"
              : "Get started by creating your first asset module"
            }
          </p>
          <Button onClick={onCreateModule}>
            <Plus className="h-4 w-4 mr-2" />
            Create Module
          </Button>
        </div>
      ) : (
        <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
          {modules.map((module) => (
            <ModuleCard
              key={module.id}
              module={module}
              viewMode={viewMode}
              onEdit={() => onEditModule(module)}
              getCategoryIcon={getCategoryIcon}
              getCategoryColor={getCategoryColor}
            />
          ))}
        </div>
      )}
    </>
  );
}

interface ModuleCardProps {
  module: AssetModule;
  viewMode: "grid" | "list";
  onEdit: () => void;
  getCategoryIcon: (category: ModuleCategory) => any;
  getCategoryColor: (category: ModuleCategory) => string;
}

function ModuleCard({
  module,
  viewMode,
  onEdit,
  getCategoryIcon,
  getCategoryColor,
}: ModuleCardProps) {
  const CategoryIcon = getCategoryIcon(module.category);

  const handleDuplicate = () => {
    // TODO: Implement module duplication
    console.log("Duplicate module:", module.id);
  };

  const handleDelete = () => {
    // TODO: Implement module deletion
    console.log("Delete module:", module.id);
  };

  const handleExport = () => {
    // TODO: Implement module export
    console.log("Export module:", module.id);
  };

  if (viewMode === "list") {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 flex-1">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 rounded-lg bg-muted flex items-center justify-center">
                  <CategoryIcon className="h-5 w-5 text-muted-foreground" />
                </div>
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="font-semibold text-sm truncate">{module.name}</h3>
                  <Badge variant="secondary" className="text-xs">v{module.version}</Badge>
                  <Badge className={`text-xs ${getCategoryColor(module.category)}`}>
                    {module.category}
                  </Badge>
                  {module.isBuiltIn && (
                    <Badge variant="outline" className="text-xs">
                      Built-in
                    </Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground truncate">{module.description}</p>
                <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    {module.usageCount} uses
                  </span>
                  <span className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {new Date(module.updatedAt).toLocaleDateString()}
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {module.isActive ? (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    ) : (
                      <AlertCircle className="h-3 w-3 text-yellow-500" />
                    )}
                    {module.isActive ? "Active" : "Inactive"}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={onEdit}>
                <Edit className="h-4 w-4 mr-1" />
                Edit
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleDuplicate}>
                    <Copy className="h-4 w-4 mr-2" />
                    Duplicate
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleExport}>
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </DropdownMenuItem>
                  {!module.isBuiltIn && (
                    <DropdownMenuItem onClick={handleDelete} className="text-destructive">
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer group">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-lg bg-muted flex items-center justify-center">
              <CategoryIcon className="h-5 w-5 text-muted-foreground" />
            </div>
            <div>
              <div className="flex items-center gap-2 mb-1">
                <CardTitle className="text-base">{module.name}</CardTitle>
                <Badge variant="secondary" className="text-xs">v{module.version}</Badge>
              </div>
              <div className="flex items-center gap-2">
                <Badge className={`text-xs ${getCategoryColor(module.category)}`}>
                  {module.category}
                </Badge>
                {module.isBuiltIn && (
                  <Badge variant="outline" className="text-xs">
                    Built-in
                  </Badge>
                )}
              </div>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={onEdit}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDuplicate}>
                <Copy className="h-4 w-4 mr-2" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleExport}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </DropdownMenuItem>
              {!module.isBuiltIn && (
                <DropdownMenuItem onClick={handleDelete} className="text-destructive">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <CardDescription className="text-sm mb-4 line-clamp-2">
          {module.description}
        </CardDescription>

        <div className="flex flex-wrap gap-1 mb-4">
          {module.tags.slice(0, 3).map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {module.tags.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{module.tags.length - 3} more
            </Badge>
          )}
        </div>

        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-4">
            <span className="flex items-center gap-1">
              <Users className="h-3 w-3" />
              {module.usageCount}
            </span>
            <span className="flex items-center gap-1">
              {module.isActive ? (
                <CheckCircle className="h-3 w-3 text-green-500" />
              ) : (
                <AlertCircle className="h-3 w-3 text-yellow-500" />
              )}
              {module.isActive ? "Active" : "Inactive"}
            </span>
          </div>
          <span>{new Date(module.updatedAt).toLocaleDateString()}</span>
        </div>

        <div className="mt-4 pt-4 border-t">
          <Button
            className="w-full"
            variant="outline"
            size="sm"
            onClick={onEdit}
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit Module
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
