"use client";

import React, { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from "@/components/ui/dropdown-menu";
import {
  Plus,
  Search,
  Filter,
  Grid,
  List,
  Star,
  Users,
  Calendar,
  Package,
  Code,
  Layers,
  AlertCircle,
  CheckCircle,
  Clock,
  Edit,
  Copy,
  Trash2,
  Download,
  Upload,
  Eye,
  Settings,
  MoreHorizontal,
  FileText,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Activity,
  Zap,
  Shield,
  Globe,
  Database,
  Cpu,
  HardDrive,
  Network,
  Workflow,
  Target,
  Award,
  Bookmark,
  Share2,
  ExternalLink,
  RefreshCw,
  Bell,
  ChevronRight,
  ArrowUpRight,
  ArrowDownRight,
  DollarSign,
  PieChart,
  LineChart,
  BarChart,
  Gauge,
  Archive
} from "lucide-react";
import { AssetModule, ModuleCategory, ModuleTemplate } from "@/lib/types/asset-modules";
import { useAdminHeader } from "@/hooks/use-admin-header";
import { useHeaderTabs } from "@/hooks/use-admin-tabs";
import { getAssetModulesHeaderConfig } from "@/lib/utils/admin-header-configs";
import { getAssetModulesHeaderTabs } from "@/lib/utils/admin-tabs-configs";
import { HeaderTabContent } from "@/components/ui/header-tab-content";
import { ModuleAnalyticsDashboard } from "./dashboard/module-analytics-dashboard";
import { ModuleMarketplaceDashboard } from "./dashboard/module-marketplace-dashboard";
import { ModuleWorkspaceDashboard } from "./module-workspace-dashboard";
import { MetricCard, EmptyState } from "./shared/component-utils";
import { DASHBOARD_METRICS } from "./shared/ui-constants";

interface AssetModuleDashboardProps {
  onCreateModule?: (template?: ModuleTemplate) => void;
  onEditModule?: (moduleId: string) => void;
}

interface DashboardMetrics {
  totalModules: number;
  activeModules: number;
  draftModules: number;
  totalUsage: number;
  monthlyGrowth: number;
  averageRating: number;
  communityModules: number;
  enterpriseModules: number;
  systemHealth: number;
  lastUpdated: string;
}

interface ModuleUsageStats {
  moduleId: string;
  moduleName: string;
  usageCount: number;
  growthRate: number;
  category: ModuleCategory;
  lastUsed: string;
}

interface SystemAlert {
  id: string;
  type: 'warning' | 'error' | 'info' | 'success';
  title: string;
  message: string;
  timestamp: string;
  actionRequired: boolean;
}

export function AssetModuleDashboard({
  onCreateModule,
  onEditModule,
}: AssetModuleDashboardProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [topModules, setTopModules] = useState<ModuleUsageStats[]>([]);
  const [systemAlerts, setSystemAlerts] = useState<SystemAlert[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  // Load dashboard data
  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      // TODO: Replace with actual API calls
      // const [metricsResponse, usageResponse, alertsResponse] = await Promise.all([
      //   fetch("/api/asset-modules/metrics"),
      //   fetch("/api/asset-modules/usage-stats"),
      //   fetch("/api/asset-modules/system-alerts")
      // ]);

      // Mock data for demonstration
      setMetrics({
        totalModules: 47,
        activeModules: 42,
        draftModules: 5,
        totalUsage: 1247,
        monthlyGrowth: 12.5,
        averageRating: 4.6,
        communityModules: 23,
        enterpriseModules: 24,
        systemHealth: 98.5,
        lastUpdated: new Date().toISOString()
      });

      setTopModules([
        {
          moduleId: "location-module",
          moduleName: "Location Module",
          usageCount: 245,
          growthRate: 15.2,
          category: "location",
          lastUsed: "2024-01-20T10:30:00Z"
        },
        {
          moduleId: "software-module",
          moduleName: "Software Tracking",
          usageCount: 189,
          growthRate: 8.7,
          category: "software",
          lastUsed: "2024-01-20T09:15:00Z"
        },
        {
          moduleId: "maintenance-module",
          moduleName: "Maintenance Scheduler",
          usageCount: 156,
          growthRate: 22.1,
          category: "maintenance",
          lastUsed: "2024-01-20T11:45:00Z"
        }
      ]);

      setSystemAlerts([
        {
          id: "alert-1",
          type: "warning",
          title: "Module Update Available",
          message: "3 modules have pending updates with security improvements",
          timestamp: "2024-01-20T08:00:00Z",
          actionRequired: true
        },
        {
          id: "alert-2",
          type: "info",
          title: "New Community Module",
          message: "Financial Compliance module is now available in the marketplace",
          timestamp: "2024-01-19T16:30:00Z",
          actionRequired: false
        }
      ]);

    } catch (error) {
      console.error("Error loading dashboard data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const handleCreateModule = () => {
    if (onCreateModule) {
      onCreateModule();
    } else {
      router.push('/module-editor/new');
    }
  };

  const handleImportModule = () => {
    router.push('/admin/asset-modules?tab=marketplace');
  };

  const handleExportModules = () => {
    // TODO: Implement bulk export functionality
    console.log("Export modules");
  };

  // Header configuration
  const headerConfig = useMemo(() => 
    getAssetModulesHeaderConfig(
      handleCreateModule,
      handleImportModule,
      handleExportModules
    ), 
    []
  );

  const headerTabs = useMemo(() => getAssetModulesHeaderTabs(), []);

  useAdminHeader(headerConfig);
  useHeaderTabs(headerTabs, "dashboard");

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
          { id: "dashboard", content: <DashboardOverview
            metrics={metrics}
            topModules={topModules}
            systemAlerts={systemAlerts}
            onRefresh={handleRefresh}
            refreshing={refreshing}
            onCreateModule={handleCreateModule}
            onEditModule={onEditModule}
          /> },
          { id: "workspace", content: <ModuleWorkspaceDashboard
            onCreateModule={onCreateModule}
            onEditModule={onEditModule}
          /> },
          { id: "templates", content: <EmptyState
            icon={Package}
            title="Module Templates"
            description="Pre-built templates to jumpstart your module development"
            action={{
              label: "Browse Templates",
              onClick: handleCreateModule
            }}
          /> },
          { id: "marketplace", content: <ModuleMarketplaceDashboard /> },
          { id: "analytics", content: <ModuleAnalyticsDashboard /> },
        ]}
      />
    </div>
  );
}

interface DashboardOverviewProps {
  metrics: DashboardMetrics | null;
  topModules: ModuleUsageStats[];
  systemAlerts: SystemAlert[];
  onRefresh: () => void;
  refreshing: boolean;
  onCreateModule: () => void;
  onEditModule?: (moduleId: string) => void;
}

function DashboardOverview({
  metrics,
  topModules,
  systemAlerts,
  onRefresh,
  refreshing,
  onCreateModule,
  onEditModule
}: DashboardOverviewProps) {
  if (!metrics) return null;

  return (
    <div className="space-y-6 py-2">
      
      {/* Key Metrics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Total Modules"
          value={metrics.totalModules}
          description={`${metrics.activeModules} active, ${metrics.draftModules} draft`}
          icon={Package}
          trend={metrics.monthlyGrowth}
          color="blue"
        />
        <MetricCard
          title="Total Usage"
          value={metrics.totalUsage}
          description="Across all modules"
          icon={Activity}
          trend={metrics.monthlyGrowth}
          color="green"
        />
        <MetricCard
          title="Average Rating"
          value={metrics.averageRating}
          description="Community feedback"
          icon={Star}
          trend={2.1}
          color="yellow"
          format="rating"
        />
        <MetricCard
          title="System Health"
          value={metrics.systemHealth}
          description="Overall performance"
          icon={Gauge}
          trend={0.5}
          color="purple"
          format="percentage"
        />
      </div>

      {/* Main Dashboard Content Grid */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Left Column - Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Top Performing Modules */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Top Performing Modules</CardTitle>
                  <CardDescription>Most used modules this month</CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-2" />
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topModules.map((module, index) => (
                  <div key={module.moduleId} className="flex items-center justify-between p-3 rounded-lg border">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-semibold text-sm">
                        {index + 1}
                      </div>
                      <div>
                        <h4 className="font-medium">{module.moduleName}</h4>
                        <p className="text-sm text-muted-foreground capitalize">
                          {module.category} • {module.usageCount} uses
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={module.growthRate > 0 ? "default" : "secondary"}>
                        {module.growthRate > 0 ? (
                          <TrendingUp className="h-3 w-3 mr-1" />
                        ) : (
                          <TrendingDown className="h-3 w-3 mr-1" />
                        )}
                        {Math.abs(module.growthRate)}%
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onEditModule?.(module.moduleId)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Module Categories Overview */}
          <Card>
            <CardHeader>
              <CardTitle>Module Categories</CardTitle>
              <CardDescription>Distribution across different categories</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <CategoryCard
                  category="location"
                  count={8}
                  icon={Globe}
                  color="blue"
                />
                <CategoryCard
                  category="software"
                  count={12}
                  icon={Code}
                  color="green"
                />
                <CategoryCard
                  category="hardware"
                  count={6}
                  icon={Cpu}
                  color="orange"
                />
                <CategoryCard
                  category="maintenance"
                  count={9}
                  icon={Settings}
                  color="purple"
                />
                <CategoryCard
                  category="financial"
                  count={4}
                  icon={DollarSign}
                  color="yellow"
                />
                <CategoryCard
                  category="compliance"
                  count={3}
                  icon={Shield}
                  color="red"
                />
                <CategoryCard
                  category="security"
                  count={2}
                  icon={Shield}
                  color="indigo"
                />
                <CategoryCard
                  category="custom"
                  count={3}
                  icon={Layers}
                  color="gray"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start" onClick={onCreateModule}>
                <Plus className="h-4 w-4 mr-2" />
                Create New Module
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Upload className="h-4 w-4 mr-2" />
                Import Module
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Globe className="h-4 w-4 mr-2" />
                Browse Marketplace
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <BarChart3 className="h-4 w-4 mr-2" />
                View Analytics
              </Button>
            </CardContent>
          </Card>

          {/* System Status */}
          <Card>
            <CardHeader>
              <CardTitle>System Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Module Runtime</span>
                  <span className="text-green-600">Healthy</span>
                </div>
                <Progress value={98} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>API Performance</span>
                  <span className="text-green-600">Optimal</span>
                </div>
                <Progress value={95} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Storage Usage</span>
                  <span className="text-yellow-600">Moderate</span>
                </div>
                <Progress value={67} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Cache Hit Rate</span>
                  <span className="text-green-600">Excellent</span>
                </div>
                <Progress value={92} className="h-2" />
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-48">
                <div className="space-y-3">
                  <ActivityItem
                    action="Module Created"
                    target="Financial Tracker v1.0"
                    time="2 hours ago"
                    icon={Plus}
                    color="green"
                  />
                  <ActivityItem
                    action="Module Updated"
                    target="Location Module v1.2.1"
                    time="4 hours ago"
                    icon={Edit}
                    color="blue"
                  />
                  <ActivityItem
                    action="Module Deployed"
                    target="Maintenance Scheduler"
                    time="6 hours ago"
                    icon={Upload}
                    color="purple"
                  />
                  <ActivityItem
                    action="Module Archived"
                    target="Legacy Tracker v0.9"
                    time="1 day ago"
                    icon={Archive}
                    color="gray"
                  />
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

// Supporting Components - moved to shared/component-utils.tsx for reusability

interface CategoryCardProps {
  category: string;
  count: number;
  icon: React.ElementType;
  color: string;
}

function CategoryCard({ category, count, icon: Icon, color }: CategoryCardProps) {
  const colorClasses = {
    blue: 'bg-blue-100 text-blue-600 border-blue-200',
    green: 'bg-green-100 text-green-600 border-green-200',
    orange: 'bg-orange-100 text-orange-600 border-orange-200',
    purple: 'bg-purple-100 text-purple-600 border-purple-200',
    yellow: 'bg-yellow-100 text-yellow-600 border-yellow-200',
    red: 'bg-red-100 text-red-600 border-red-200',
    indigo: 'bg-indigo-100 text-indigo-600 border-indigo-200',
    gray: 'bg-gray-100 text-gray-600 border-gray-200'
  };

  return (
    <div className={`p-3 rounded-lg border ${colorClasses[color as keyof typeof colorClasses] || colorClasses.gray}`}>
      <div className="flex items-center justify-between">
        <Icon className="h-5 w-5" />
        <span className="text-lg font-semibold">{count}</span>
      </div>
      <p className="text-sm font-medium capitalize mt-1">{category}</p>
    </div>
  );
}

interface ActivityItemProps {
  action: string;
  target: string;
  time: string;
  icon: React.ElementType;
  color: string;
}

function ActivityItem({ action, target, time, icon: Icon, color }: ActivityItemProps) {
  const colorClasses = {
    green: 'text-green-600 bg-green-100',
    blue: 'text-blue-600 bg-blue-100',
    purple: 'text-purple-600 bg-purple-100',
    gray: 'text-gray-600 bg-gray-100'
  };

  return (
    <div className="flex items-start gap-3">
      <div className={`p-1 rounded-full ${colorClasses[color as keyof typeof colorClasses] || colorClasses.gray}`}>
        <Icon className="h-3 w-3" />
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium">{action}</p>
        <p className="text-xs text-muted-foreground truncate">{target}</p>
        <p className="text-xs text-muted-foreground">{time}</p>
      </div>
    </div>
  );
}
