"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tabs, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Search,
  Star,
  Download,
  Upload,
  Users,
  Eye,
  Package,
  Verified,
  Crown,
  Sparkles,
  CheckCircle,
  Heart
} from "lucide-react";
import { MetricCard } from "../shared/component-utils";
import { MARKETPLACE_ICONS } from "../shared/ui-constants";
import { AssetModule, ModuleCategory } from "@/lib/types/asset-modules";

interface MarketplaceModule {
  id: string;
  name: string;
  description: string;
  category: ModuleCategory;
  author: {
    name: string;
    avatar?: string;
    verified: boolean;
    company?: string;
  };
  version: string;
  rating: number;
  reviewCount: number;
  downloads: number;
  price: number; // 0 for free
  tags: string[];
  screenshots: string[];
  features: string[];
  compatibility: string[];
  lastUpdated: string;
  isPremium: boolean;
  isVerified: boolean;
  isFeatured: boolean;
}

interface MarketplaceStats {
  totalModules: number;
  freeModules: number;
  premiumModules: number;
  totalDownloads: number;
  averageRating: number;
  activePublishers: number;
}

export function ModuleMarketplaceDashboard() {
  const [modules, setModules] = useState<MarketplaceModule[]>([]);
  const [stats, setStats] = useState<MarketplaceStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<ModuleCategory | "all">("all");
  const [sortBy, setSortBy] = useState("popular");
  const [priceFilter, setPriceFilter] = useState("all");

  useEffect(() => {
    loadMarketplaceData();
  }, []);

  const loadMarketplaceData = async () => {
    try {
      setIsLoading(true);
      
      // Mock data - replace with actual API calls
      const mockModules: MarketplaceModule[] = [
        {
          id: "advanced-location-tracker",
          name: "Advanced Location Tracker Pro",
          description: "Professional location tracking with GPS, geofencing, and advanced mapping capabilities",
          category: "location",
          author: {
            name: "GeoTech Solutions",
            avatar: "/avatars/geotech.png",
            verified: true,
            company: "GeoTech Solutions Inc."
          },
          version: "2.1.0",
          rating: 4.8,
          reviewCount: 127,
          downloads: 2847,
          price: 49.99,
          tags: ["GPS", "Geofencing", "Maps", "Professional"],
          screenshots: [],
          features: [
            "Real-time GPS tracking",
            "Geofencing alerts",
            "Custom map layers",
            "Batch location import",
            "API integrations"
          ],
          compatibility: ["v1.0+"],
          lastUpdated: "2024-01-15T10:00:00Z",
          isPremium: true,
          isVerified: true,
          isFeatured: true
        },
        {
          id: "financial-compliance-suite",
          name: "Financial Compliance Suite",
          description: "Complete financial tracking and compliance reporting for enterprise assets",
          category: "financial",
          author: {
            name: "FinanceFlow",
            avatar: "/avatars/financeflow.png",
            verified: true,
            company: "FinanceFlow Ltd."
          },
          version: "1.5.2",
          rating: 4.6,
          reviewCount: 89,
          downloads: 1234,
          price: 79.99,
          tags: ["Compliance", "Reporting", "Enterprise", "Audit"],
          screenshots: [],
          features: [
            "Automated compliance reports",
            "Audit trail tracking",
            "Multi-currency support",
            "Tax calculation",
            "Integration with accounting systems"
          ],
          compatibility: ["v1.0+"],
          lastUpdated: "2024-01-10T14:30:00Z",
          isPremium: true,
          isVerified: true,
          isFeatured: true
        },
        {
          id: "open-maintenance-scheduler",
          name: "Open Maintenance Scheduler",
          description: "Free, open-source maintenance scheduling with basic workflow automation",
          category: "maintenance",
          author: {
            name: "Community",
            verified: false
          },
          version: "1.2.1",
          rating: 4.2,
          reviewCount: 156,
          downloads: 3421,
          price: 0,
          tags: ["Open Source", "Scheduling", "Workflow", "Free"],
          screenshots: [],
          features: [
            "Basic scheduling",
            "Task assignments",
            "Email notifications",
            "Simple reporting",
            "Open source"
          ],
          compatibility: ["v1.0+"],
          lastUpdated: "2024-01-18T09:00:00Z",
          isPremium: false,
          isVerified: false,
          isFeatured: false
        }
      ];

      const mockStats: MarketplaceStats = {
        totalModules: 247,
        freeModules: 156,
        premiumModules: 91,
        totalDownloads: 45672,
        averageRating: 4.3,
        activePublishers: 89
      };

      setModules(mockModules);
      setStats(mockStats);
    } catch (error) {
      console.error("Error loading marketplace data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredModules = modules.filter(module => {
    const matchesSearch = module.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         module.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         module.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === "all" || module.category === selectedCategory;
    
    const matchesPrice = priceFilter === "all" || 
                        (priceFilter === "free" && module.price === 0) ||
                        (priceFilter === "premium" && module.price > 0);
    
    return matchesSearch && matchesCategory && matchesPrice;
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <Package className="h-8 w-8 animate-pulse mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">Loading marketplace...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Marketplace Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Module Marketplace</h2>
          <p className="text-muted-foreground">
            Discover and install modules from the community
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Upload className="h-4 w-4 mr-2" />
            Publish Module
          </Button>
          <Button>
            <Package className="h-4 w-4 mr-2" />
            My Modules
          </Button>
        </div>
      </div>

      {/* Marketplace Stats */}
      {stats && (
        <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-6">
          <MetricCard title="Total Modules" value={stats.totalModules} icon={Package} color="blue" />
          <MetricCard title="Free Modules" value={stats.freeModules} icon={Heart} color="green" />
          <MetricCard title="Premium Modules" value={stats.premiumModules} icon={Crown} color="yellow" />
          <MetricCard title="Total Downloads" value={stats.totalDownloads} icon={Download} color="purple" />
          <MetricCard title="Avg Rating" value={stats.averageRating} icon={Star} format="rating" color="yellow" />
          <MetricCard title="Publishers" value={stats.activePublishers} icon={Users} color="indigo" />
        </div>
      )}

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search modules..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <Select value={selectedCategory} onValueChange={(value) => setSelectedCategory(value as ModuleCategory | "all")}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="location">Location</SelectItem>
              <SelectItem value="software">Software</SelectItem>
              <SelectItem value="hardware">Hardware</SelectItem>
              <SelectItem value="financial">Financial</SelectItem>
              <SelectItem value="maintenance">Maintenance</SelectItem>
              <SelectItem value="compliance">Compliance</SelectItem>
              <SelectItem value="security">Security</SelectItem>
              <SelectItem value="custom">Custom</SelectItem>
            </SelectContent>
          </Select>
          <Select value={priceFilter} onValueChange={setPriceFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Price" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Prices</SelectItem>
              <SelectItem value="free">Free</SelectItem>
              <SelectItem value="premium">Premium</SelectItem>
            </SelectContent>
          </Select>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="popular">Popular</SelectItem>
              <SelectItem value="newest">Newest</SelectItem>
              <SelectItem value="rating">Rating</SelectItem>
              <SelectItem value="price">Price</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Featured Modules */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-yellow-500" />
          Featured Modules
        </h3>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {modules.filter(m => m.isFeatured).map((module) => (
            <ModuleCard key={module.id} module={module} featured />
          ))}
        </div>
      </div>

      {/* All Modules */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">All Modules</h3>
          <p className="text-sm text-muted-foreground">
            {filteredModules.length} modules found
          </p>
        </div>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredModules.map((module) => (
            <ModuleCard key={module.id} module={module} />
          ))}
        </div>
      </div>
    </div>
  );
}

// StatCard component replaced with shared MetricCard component

interface ModuleCardProps {
  module: MarketplaceModule;
  featured?: boolean;
}

function ModuleCard({ module, featured = false }: ModuleCardProps) {
  return (
    <Card className={`relative ${featured ? 'ring-2 ring-yellow-500/20 bg-gradient-to-br from-yellow-50/50 to-transparent' : ''}`}>
      {featured && (
        <div className="absolute -top-2 -right-2 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
          Featured
        </div>
      )}
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <CardTitle className="text-base">{module.name}</CardTitle>
              {module.isVerified && (
                <Verified className="h-4 w-4 text-blue-500" />
              )}
              {module.isPremium && (
                <Crown className="h-4 w-4 text-yellow-500" />
              )}
            </div>
            <CardDescription className="text-sm line-clamp-2">
              {module.description}
            </CardDescription>
          </div>
        </div>
        
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <Avatar className="h-4 w-4">
              <AvatarImage src={module.author.avatar} />
              <AvatarFallback className="text-xs">
                {module.author.name.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <span>{module.author.name}</span>
            {module.author.verified && (
              <CheckCircle className="h-3 w-3 text-blue-500" />
            )}
          </div>
          <Badge variant="outline" className="text-xs capitalize">
            {module.category}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-1">
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            <span className="text-sm font-medium">{module.rating}</span>
            <span className="text-xs text-muted-foreground">
              ({module.reviewCount})
            </span>
          </div>
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Download className="h-3 w-3" />
            {module.downloads.toLocaleString()}
          </div>
        </div>
        
        <div className="flex flex-wrap gap-1 mb-3">
          {module.tags.slice(0, 3).map((tag) => (
            <Badge key={tag} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
          {module.tags.length > 3 && (
            <Badge variant="secondary" className="text-xs">
              +{module.tags.length - 3}
            </Badge>
          )}
        </div>
        
        <div className="flex items-center justify-between">
          <div className="text-lg font-bold">
            {module.price === 0 ? (
              <span className="text-green-600">Free</span>
            ) : (
              <span>${module.price}</span>
            )}
          </div>
          <div className="flex gap-1">
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
            <Button size="sm">
              <Download className="h-4 w-4 mr-1" />
              Install
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
