"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  BarChart3,
  Activity,
  Users,
  Zap,
  CheckCircle,
  Download,
  RefreshCw,
  <PERSON><PERSON><PERSON>,
  <PERSON>Chart,
  Gauge
} from "lucide-react";
import { MetricCard } from "../shared/component-utils";

interface AnalyticsData {
  usageMetrics: {
    totalUsage: number;
    monthlyGrowth: number;
    activeUsers: number;
    averageSessionTime: number;
  };
  performanceMetrics: {
    averageLoadTime: number;
    errorRate: number;
    uptime: number;
    throughput: number;
  };
  moduleBreakdown: {
    category: string;
    usage: number;
    growth: number;
    color: string;
  }[];
  topModules: {
    name: string;
    usage: number;
    performance: number;
    satisfaction: number;
  }[];
  timeSeriesData: {
    date: string;
    usage: number;
    performance: number;
    errors: number;
  }[];
}

export function ModuleAnalyticsDashboard() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState("30d");
  const [selectedMetric, setSelectedMetric] = useState("usage");

  useEffect(() => {
    loadAnalyticsData();
  }, [timeRange]);

  const loadAnalyticsData = async () => {
    try {
      setIsLoading(true);
      
      // Mock data - replace with actual API call
      const mockData: AnalyticsData = {
        usageMetrics: {
          totalUsage: 12847,
          monthlyGrowth: 15.2,
          activeUsers: 342,
          averageSessionTime: 24.5
        },
        performanceMetrics: {
          averageLoadTime: 1.2,
          errorRate: 0.3,
          uptime: 99.8,
          throughput: 1250
        },
        moduleBreakdown: [
          { category: "Location", usage: 2845, growth: 12.3, color: "blue" },
          { category: "Software", usage: 2234, growth: 8.7, color: "green" },
          { category: "Maintenance", usage: 1987, growth: 22.1, color: "purple" },
          { category: "Hardware", usage: 1654, growth: -2.4, color: "orange" },
          { category: "Financial", usage: 1432, growth: 18.9, color: "yellow" },
          { category: "Compliance", usage: 987, growth: 5.6, color: "red" },
          { category: "Security", usage: 876, growth: 31.2, color: "indigo" },
          { category: "Custom", usage: 832, growth: 7.8, color: "gray" }
        ],
        topModules: [
          { name: "Location Tracker", usage: 2845, performance: 98.5, satisfaction: 4.8 },
          { name: "Software Manager", usage: 2234, performance: 97.2, satisfaction: 4.6 },
          { name: "Maintenance Scheduler", usage: 1987, performance: 99.1, satisfaction: 4.9 },
          { name: "Asset Monitor", usage: 1654, performance: 96.8, satisfaction: 4.4 },
          { name: "Financial Tracker", usage: 1432, performance: 98.9, satisfaction: 4.7 }
        ],
        timeSeriesData: [
          { date: "2024-01-01", usage: 8500, performance: 97.5, errors: 12 },
          { date: "2024-01-08", usage: 9200, performance: 98.1, errors: 8 },
          { date: "2024-01-15", usage: 10100, performance: 97.8, errors: 15 },
          { date: "2024-01-22", usage: 11500, performance: 98.5, errors: 6 },
          { date: "2024-01-29", usage: 12847, performance: 98.9, errors: 4 }
        ]
      };

      setAnalyticsData(mockData);
    } catch (error) {
      console.error("Error loading analytics data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">Loading analytics...</p>
        </div>
      </div>
    );
  }

  if (!analyticsData) return null;

  return (
    <div className="space-y-6">
      {/* Analytics Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Module Analytics</h2>
          <p className="text-muted-foreground">
            Comprehensive insights into module usage and performance
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Performance Indicators */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Total Usage"
          value={analyticsData.usageMetrics.totalUsage}
          trend={analyticsData.usageMetrics.monthlyGrowth}
          icon={Activity}
          color="blue"
        />
        <MetricCard
          title="Active Users"
          value={analyticsData.usageMetrics.activeUsers}
          trend={8.3}
          icon={Users}
          color="green"
        />
        <MetricCard
          title="Avg Load Time"
          value={analyticsData.performanceMetrics.averageLoadTime}
          trend={-12.5}
          icon={Zap}
          color="yellow"
          format="number"
          description="seconds"
        />
        <MetricCard
          title="System Uptime"
          value={analyticsData.performanceMetrics.uptime}
          trend={0.2}
          icon={CheckCircle}
          color="green"
          format="percentage"
        />
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="usage">Usage Analytics</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="modules">Module Breakdown</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 lg:grid-cols-2">
            {/* Usage Trends Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Usage Trends</CardTitle>
                <CardDescription>Module usage over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                  <div className="text-center">
                    <LineChart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">Usage Trend Chart</p>
                    <p className="text-sm text-muted-foreground">Interactive chart will be rendered here</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Category Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Category Distribution</CardTitle>
                <CardDescription>Usage by module category</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analyticsData.moduleBreakdown.map((category) => (
                    <div key={category.category} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full bg-${category.color}-500`} />
                        <span className="text-sm font-medium">{category.category}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          {category.usage.toLocaleString()}
                        </span>
                        <Badge variant={category.growth > 0 ? "default" : "secondary"} className="text-xs">
                          {category.growth > 0 ? "+" : ""}{category.growth}%
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Top Performing Modules */}
          <Card>
            <CardHeader>
              <CardTitle>Top Performing Modules</CardTitle>
              <CardDescription>Modules ranked by usage and performance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.topModules.map((module, index) => (
                  <div key={module.name} className="flex items-center justify-between p-3 rounded-lg border">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-semibold text-sm">
                        {index + 1}
                      </div>
                      <div>
                        <h4 className="font-medium">{module.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {module.usage.toLocaleString()} uses
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <p className="text-sm font-medium">{module.performance}%</p>
                        <p className="text-xs text-muted-foreground">Performance</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">{module.satisfaction}/5</p>
                        <p className="text-xs text-muted-foreground">Rating</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="usage">
          <Card>
            <CardHeader>
              <CardTitle>Usage Analytics</CardTitle>
              <CardDescription>Detailed usage patterns and trends</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-96 flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                <div className="text-center">
                  <BarChart3 className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <p className="text-lg font-medium text-muted-foreground">Usage Analytics Dashboard</p>
                  <p className="text-sm text-muted-foreground">Detailed usage charts and metrics will be displayed here</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance">
          <Card>
            <CardHeader>
              <CardTitle>Performance Metrics</CardTitle>
              <CardDescription>System performance and reliability data</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-96 flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                <div className="text-center">
                  <Gauge className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <p className="text-lg font-medium text-muted-foreground">Performance Dashboard</p>
                  <p className="text-sm text-muted-foreground">Performance metrics and monitoring data</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="modules">
          <Card>
            <CardHeader>
              <CardTitle>Module Breakdown</CardTitle>
              <CardDescription>Individual module performance and usage</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-96 flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                <div className="text-center">
                  <PieChart className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <p className="text-lg font-medium text-muted-foreground">Module Analysis</p>
                  <p className="text-sm text-muted-foreground">Detailed breakdown by individual modules</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// KPICard component moved to shared/component-utils.tsx as MetricCard
