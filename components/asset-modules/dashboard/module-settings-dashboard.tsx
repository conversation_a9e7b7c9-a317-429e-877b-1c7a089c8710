"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON>ton } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import {
  Settings,
  Shield,
  Users,
  Database,
  Globe,
  Bell,
  Lock,
  Key,
  Server,
  Activity,
  FileText,
  Download,
  Upload,
  Refresh<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  Info,
  Trash2,
  Copy,
  Edit,
  Save,
  X,
  Plus,
  Minus,
  Eye,
  EyeOff,
  Zap,
  Clock,
  Target,
  BarChart3,
  Cpu,
  HardDrive,
  Network,
  Monitor
} from "lucide-react";

interface SystemSettings {
  general: {
    systemName: string;
    description: string;
    timezone: string;
    language: string;
    dateFormat: string;
    enableDebugMode: boolean;
    enableMaintenanceMode: boolean;
  };
  security: {
    enableTwoFactor: boolean;
    sessionTimeout: number;
    passwordPolicy: {
      minLength: number;
      requireUppercase: boolean;
      requireNumbers: boolean;
      requireSymbols: boolean;
    };
    enableAuditLogging: boolean;
    enableEncryption: boolean;
  };
  performance: {
    cacheEnabled: boolean;
    cacheTimeout: number;
    maxConcurrentUsers: number;
    enableCompression: boolean;
    enableCDN: boolean;
  };
  integrations: {
    enableAPIAccess: boolean;
    apiRateLimit: number;
    webhookEnabled: boolean;
    webhookUrl: string;
    enableSSOIntegration: boolean;
  };
  notifications: {
    enableEmailNotifications: boolean;
    enablePushNotifications: boolean;
    enableSlackIntegration: boolean;
    slackWebhookUrl: string;
  };
}

export function ModuleSettingsDashboard() {
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [activeTab, setActiveTab] = useState("general");

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      
      // Mock data - replace with actual API call
      const mockSettings: SystemSettings = {
        general: {
          systemName: "WizeAssets Module System",
          description: "Enterprise Asset Module Management Platform",
          timezone: "UTC",
          language: "en",
          dateFormat: "MM/DD/YYYY",
          enableDebugMode: false,
          enableMaintenanceMode: false
        },
        security: {
          enableTwoFactor: true,
          sessionTimeout: 30,
          passwordPolicy: {
            minLength: 8,
            requireUppercase: true,
            requireNumbers: true,
            requireSymbols: true
          },
          enableAuditLogging: true,
          enableEncryption: true
        },
        performance: {
          cacheEnabled: true,
          cacheTimeout: 3600,
          maxConcurrentUsers: 1000,
          enableCompression: true,
          enableCDN: false
        },
        integrations: {
          enableAPIAccess: true,
          apiRateLimit: 1000,
          webhookEnabled: false,
          webhookUrl: "",
          enableSSOIntegration: false
        },
        notifications: {
          enableEmailNotifications: true,
          enablePushNotifications: false,
          enableSlackIntegration: false,
          slackWebhookUrl: ""
        }
      };

      setSettings(mockSettings);
    } catch (error) {
      console.error("Error loading settings:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!settings) return;
    
    try {
      setIsSaving(true);
      
      // Mock API call - replace with actual save
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setHasChanges(false);
      // Show success message
    } catch (error) {
      console.error("Error saving settings:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const updateSettings = (section: keyof SystemSettings, updates: any) => {
    if (!settings) return;
    
    setSettings({
      ...settings,
      [section]: {
        ...settings[section],
        ...updates
      }
    });
    setHasChanges(true);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">Loading settings...</p>
        </div>
      </div>
    );
  }

  if (!settings) return null;

  return (
    <div className="space-y-6">
      {/* Settings Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">System Settings</h2>
          <p className="text-muted-foreground">
            Configure system behavior and integrations
          </p>
        </div>
        <div className="flex items-center gap-2">
          {hasChanges && (
            <Badge variant="outline" className="text-yellow-600 border-yellow-600">
              Unsaved Changes
            </Badge>
          )}
          <Button
            onClick={handleSave}
            disabled={!hasChanges || isSaving}
          >
            {isSaving ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Changes
          </Button>
        </div>
      </div>

      {/* System Status Alert */}
      <Alert>
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          System is running normally. All services are operational.
        </AlertDescription>
      </Alert>

      {/* Settings Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>Basic system configuration</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="systemName">System Name</Label>
                  <Input
                    id="systemName"
                    value={settings.general.systemName}
                    onChange={(e) => updateSettings('general', { systemName: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="timezone">Timezone</Label>
                  <Select
                    value={settings.general.timezone}
                    onValueChange={(value) => updateSettings('general', { timezone: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="UTC">UTC</SelectItem>
                      <SelectItem value="EST">Eastern Time</SelectItem>
                      <SelectItem value="PST">Pacific Time</SelectItem>
                      <SelectItem value="GMT">Greenwich Mean Time</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={settings.general.description}
                  onChange={(e) => updateSettings('general', { description: e.target.value })}
                  rows={3}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Debug Mode</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable detailed logging and error reporting
                  </p>
                </div>
                <Switch
                  checked={settings.general.enableDebugMode}
                  onCheckedChange={(checked) => updateSettings('general', { enableDebugMode: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Maintenance Mode</Label>
                  <p className="text-sm text-muted-foreground">
                    Temporarily disable system access for maintenance
                  </p>
                </div>
                <Switch
                  checked={settings.general.enableMaintenanceMode}
                  onCheckedChange={(checked) => updateSettings('general', { enableMaintenanceMode: checked })}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>Configure security policies and authentication</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Two-Factor Authentication</Label>
                  <p className="text-sm text-muted-foreground">
                    Require 2FA for all user accounts
                  </p>
                </div>
                <Switch
                  checked={settings.security.enableTwoFactor}
                  onCheckedChange={(checked) => updateSettings('security', { enableTwoFactor: checked })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
                <Input
                  id="sessionTimeout"
                  type="number"
                  value={settings.security.sessionTimeout}
                  onChange={(e) => updateSettings('security', { sessionTimeout: parseInt(e.target.value) })}
                />
              </div>

              <div className="space-y-3">
                <Label>Password Policy</Label>
                <div className="space-y-2 pl-4 border-l-2 border-muted">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Minimum length: {settings.security.passwordPolicy.minLength}</span>
                    <Input
                      type="number"
                      value={settings.security.passwordPolicy.minLength}
                      onChange={(e) => updateSettings('security', {
                        passwordPolicy: {
                          ...settings.security.passwordPolicy,
                          minLength: parseInt(e.target.value)
                        }
                      })}
                      className="w-20"
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Require uppercase letters</span>
                    <Switch
                      checked={settings.security.passwordPolicy.requireUppercase}
                      onCheckedChange={(checked) => updateSettings('security', {
                        passwordPolicy: {
                          ...settings.security.passwordPolicy,
                          requireUppercase: checked
                        }
                      })}
                    />
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Audit Logging</Label>
                  <p className="text-sm text-muted-foreground">
                    Log all user actions and system events
                  </p>
                </div>
                <Switch
                  checked={settings.security.enableAuditLogging}
                  onCheckedChange={(checked) => updateSettings('security', { enableAuditLogging: checked })}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Settings</CardTitle>
              <CardDescription>Optimize system performance and resource usage</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Enable Caching</Label>
                  <p className="text-sm text-muted-foreground">
                    Cache frequently accessed data for better performance
                  </p>
                </div>
                <Switch
                  checked={settings.performance.cacheEnabled}
                  onCheckedChange={(checked) => updateSettings('performance', { cacheEnabled: checked })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="cacheTimeout">Cache Timeout (seconds)</Label>
                <Input
                  id="cacheTimeout"
                  type="number"
                  value={settings.performance.cacheTimeout}
                  onChange={(e) => updateSettings('performance', { cacheTimeout: parseInt(e.target.value) })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxUsers">Max Concurrent Users</Label>
                <Input
                  id="maxUsers"
                  type="number"
                  value={settings.performance.maxConcurrentUsers}
                  onChange={(e) => updateSettings('performance', { maxConcurrentUsers: parseInt(e.target.value) })}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integrations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Integration Settings</CardTitle>
              <CardDescription>Configure external integrations and API access</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Enable API Access</Label>
                  <p className="text-sm text-muted-foreground">
                    Allow external applications to access the API
                  </p>
                </div>
                <Switch
                  checked={settings.integrations.enableAPIAccess}
                  onCheckedChange={(checked) => updateSettings('integrations', { enableAPIAccess: checked })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="apiRateLimit">API Rate Limit (requests/hour)</Label>
                <Input
                  id="apiRateLimit"
                  type="number"
                  value={settings.integrations.apiRateLimit}
                  onChange={(e) => updateSettings('integrations', { apiRateLimit: parseInt(e.target.value) })}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
              <CardDescription>Configure notification channels and preferences</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Email Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Send notifications via email
                  </p>
                </div>
                <Switch
                  checked={settings.notifications.enableEmailNotifications}
                  onCheckedChange={(checked) => updateSettings('notifications', { enableEmailNotifications: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Push Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Send browser push notifications
                  </p>
                </div>
                <Switch
                  checked={settings.notifications.enablePushNotifications}
                  onCheckedChange={(checked) => updateSettings('notifications', { enablePushNotifications: checked })}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
