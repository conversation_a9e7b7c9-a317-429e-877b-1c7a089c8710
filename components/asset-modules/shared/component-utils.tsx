// Shared component utilities for Asset Module Editor
// This file contains reusable component patterns and utilities

import React from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { Progress } from "@/components/ui/progress";
import { 
  ArrowUpRight, 
  ArrowDownRight, 
  LucideIcon,
  TrendingUp,
  TrendingDown
} from "lucide-react";
import { getCategoryConfig, getStatusConfig, getMetricConfig } from "./ui-constants";
import { ModuleCategory } from "@/lib/types/asset-modules";

// Reusable Metric Card Component
interface MetricCardProps {
  title: string;
  value: number | string;
  description?: string;
  icon: LucideIcon;
  trend?: number;
  color: 'blue' | 'green' | 'yellow' | 'purple' | 'red' | 'indigo';
  format?: 'number' | 'percentage' | 'rating' | 'currency';
  className?: string;
}

export function MetricCard({ 
  title, 
  value, 
  description, 
  icon: Icon, 
  trend, 
  color, 
  format = 'number',
  className = ""
}: MetricCardProps) {
  const formatValue = (val: number | string) => {
    if (typeof val === 'string') return val;
    
    switch (format) {
      case 'percentage':
        return `${val}%`;
      case 'rating':
        return `${val}/5`;
      case 'currency':
        return `$${val.toLocaleString()}`;
      default:
        return val.toLocaleString();
    }
  };

  const colorClasses = {
    blue: 'text-blue-600',
    green: 'text-green-600',
    yellow: 'text-yellow-600',
    purple: 'text-purple-600',
    red: 'text-red-600',
    indigo: 'text-indigo-600'
  };

  return (
    <Card className={`hover:shadow-md transition-shadow duration-200 ${className}`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className={`p-2 rounded-full bg-${color}-100`}>
          <Icon className={`h-4 w-4 ${colorClasses[color]}`} />
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{formatValue(value)}</div>
        <div className="flex items-center justify-between mt-2">
          {description && (
            <p className="text-xs text-muted-foreground">{description}</p>
          )}
          {trend !== undefined && (
            <div className={`flex items-center text-xs ${
              trend > 0 ? 'text-green-600' : trend < 0 ? 'text-red-600' : 'text-gray-600'
            }`}>
              {trend > 0 ? (
                <ArrowUpRight className="h-3 w-3 mr-1" />
              ) : trend < 0 ? (
                <ArrowDownRight className="h-3 w-3 mr-1" />
              ) : null}
              {Math.abs(trend)}%
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Reusable Category Badge Component
interface CategoryBadgeProps {
  category: ModuleCategory;
  showIcon?: boolean;
  variant?: 'default' | 'outline' | 'secondary';
  className?: string;
}

export function CategoryBadge({ 
  category, 
  showIcon = true, 
  variant = 'outline',
  className = ""
}: CategoryBadgeProps) {
  const config = getCategoryConfig(category);
  const Icon = config.icon;

  return (
    <Badge 
      variant={variant} 
      className={`${config.textColor} ${config.borderColor} capitalize ${className}`}
    >
      {showIcon && <Icon className="h-3 w-3 mr-1" />}
      {category}
    </Badge>
  );
}

// Reusable Status Indicator Component
interface StatusIndicatorProps {
  status: 'success' | 'warning' | 'error' | 'info';
  text: string;
  showIcon?: boolean;
  className?: string;
}

export function StatusIndicator({ 
  status, 
  text, 
  showIcon = true,
  className = ""
}: StatusIndicatorProps) {
  const config = getStatusConfig(status);
  const Icon = config.icon;

  return (
    <div className={`flex items-center gap-2 px-3 py-2 rounded-md ${config.bg} ${config.border} border ${className}`}>
      {showIcon && <Icon className={`h-4 w-4 ${config.text}`} />}
      <span className={`text-sm font-medium ${config.text}`}>{text}</span>
    </div>
  );
}

// Reusable Progress Card Component
interface ProgressCardProps {
  title: string;
  value: number;
  max?: number;
  description?: string;
  color?: 'blue' | 'green' | 'yellow' | 'red';
  className?: string;
}

export function ProgressCard({ 
  title, 
  value, 
  max = 100, 
  description,
  color = 'blue',
  className = ""
}: ProgressCardProps) {
  const percentage = Math.round((value / max) * 100);
  
  return (
    <Card className={className}>
      <CardContent className="p-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="font-medium">{title}</span>
            <span className="text-muted-foreground">{percentage}%</span>
          </div>
          <Progress value={percentage} className="h-2" />
          {description && (
            <p className="text-xs text-muted-foreground">{description}</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Reusable Action Button with Tooltip
interface ActionButtonProps {
  icon: LucideIcon;
  tooltip: string;
  onClick: () => void;
  variant?: 'default' | 'outline' | 'ghost' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
}

export function ActionButton({ 
  icon: Icon, 
  tooltip, 
  onClick, 
  variant = 'ghost',
  size = 'sm',
  disabled = false,
  className = ""
}: ActionButtonProps) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant={variant}
          size={size}
          onClick={onClick}
          disabled={disabled}
          className={className}
        >
          <Icon className="h-4 w-4" />
        </Button>
      </TooltipTrigger>
      <TooltipContent>{tooltip}</TooltipContent>
    </Tooltip>
  );
}

// Reusable Trend Indicator
interface TrendIndicatorProps {
  value: number;
  format?: 'percentage' | 'number';
  showIcon?: boolean;
  className?: string;
}

export function TrendIndicator({ 
  value, 
  format = 'percentage',
  showIcon = true,
  className = ""
}: TrendIndicatorProps) {
  const isPositive = value > 0;
  const isNegative = value < 0;
  
  const formatValue = () => {
    const absValue = Math.abs(value);
    return format === 'percentage' ? `${absValue}%` : absValue.toString();
  };

  return (
    <div className={`flex items-center text-xs ${
      isPositive ? 'text-green-600' : 
      isNegative ? 'text-red-600' : 
      'text-gray-600'
    } ${className}`}>
      {showIcon && (
        <>
          {isPositive && <TrendingUp className="h-3 w-3 mr-1" />}
          {isNegative && <TrendingDown className="h-3 w-3 mr-1" />}
        </>
      )}
      {formatValue()}
    </div>
  );
}

// Reusable Empty State Component
interface EmptyStateProps {
  icon: LucideIcon;
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
}

export function EmptyState({ 
  icon: Icon, 
  title, 
  description, 
  action,
  className = ""
}: EmptyStateProps) {
  return (
    <div className={`text-center py-12 ${className}`}>
      <Icon className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground mb-4 max-w-md mx-auto">{description}</p>
      {action && (
        <Button onClick={action.onClick}>
          {action.label}
        </Button>
      )}
    </div>
  );
}

// Utility function to generate consistent class names
export const cn = (...classes: (string | undefined | null | false)[]) => {
  return classes.filter(Boolean).join(' ');
};

// Common loading skeleton
export function LoadingSkeleton({ className = "" }: { className?: string }) {
  return (
    <div className={`animate-pulse ${className}`}>
      <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
      <div className="h-4 bg-muted rounded w-1/2"></div>
    </div>
  );
}
