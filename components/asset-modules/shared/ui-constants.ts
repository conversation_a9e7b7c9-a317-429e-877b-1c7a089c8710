// Shared UI constants for Asset Module Editor components
// This file consolidates common styling, colors, and UI patterns

import {
  Layers,
  Workflow,
  Palette,
  Shield,
  Eye,
  Settings,
  Code,
  Database,
  Globe,
  Cpu,
  DollarSign,
  AlertTriangle,
  Package,
  Users,
  Star,
  Download,
  CheckCircle,
  Clock,
  Activity,
  TrendingUp,
  BarChart3,
  PieChart,
  Gauge,
  Heart,
  Crown,
  Sparkles,
  Verified
} from "lucide-react";

// Module Editor Tab Configuration
export const MODULE_EDITOR_TABS = [
  {
    id: 'fields',
    title: 'Fields',
    type: 'fields',
    icon: Layers,
    description: 'Define module fields and data structure'
  },
  {
    id: 'logic',
    title: 'Logic',
    type: 'logic',
    icon: Workflow,
    description: 'Configure business logic and workflows'
  },
  {
    id: 'rendering',
    title: 'Rendering',
    type: 'rendering',
    icon: Palette,
    description: 'Design form layouts and display components'
  },
  {
    id: 'validation',
    title: 'Validation',
    type: 'validation',
    icon: Shield,
    description: 'Set up validation rules and constraints'
  },
  {
    id: 'preview',
    title: 'Preview',
    type: 'preview',
    icon: Eye,
    description: 'Preview module in different contexts'
  }
] as const;

// Module Categories with Icons and Colors
export const MODULE_CATEGORIES = {
  location: {
    icon: Globe,
    color: 'blue',
    bgColor: 'bg-blue-100',
    textColor: 'text-blue-600',
    borderColor: 'border-blue-200'
  },
  software: {
    icon: Code,
    color: 'green',
    bgColor: 'bg-green-100',
    textColor: 'text-green-600',
    borderColor: 'border-green-200'
  },
  hardware: {
    icon: Cpu,
    color: 'orange',
    bgColor: 'bg-orange-100',
    textColor: 'text-orange-600',
    borderColor: 'border-orange-200'
  },
  financial: {
    icon: DollarSign,
    color: 'yellow',
    bgColor: 'bg-yellow-100',
    textColor: 'text-yellow-600',
    borderColor: 'border-yellow-200'
  },
  maintenance: {
    icon: Settings,
    color: 'purple',
    bgColor: 'bg-purple-100',
    textColor: 'text-purple-600',
    borderColor: 'border-purple-200'
  },
  compliance: {
    icon: Shield,
    color: 'red',
    bgColor: 'bg-red-100',
    textColor: 'text-red-600',
    borderColor: 'border-red-200'
  },
  security: {
    icon: Shield,
    color: 'indigo',
    bgColor: 'bg-indigo-100',
    textColor: 'text-indigo-600',
    borderColor: 'border-indigo-200'
  },
  custom: {
    icon: Package,
    color: 'gray',
    bgColor: 'bg-gray-100',
    textColor: 'text-gray-600',
    borderColor: 'border-gray-200'
  }
} as const;

// Dashboard Metric Icons
export const DASHBOARD_METRICS = {
  totalModules: { icon: Package, color: 'blue' },
  activeModules: { icon: CheckCircle, color: 'green' },
  totalUsage: { icon: Activity, color: 'purple' },
  averageRating: { icon: Star, color: 'yellow' },
  systemHealth: { icon: Gauge, color: 'green' },
  downloads: { icon: Download, color: 'indigo' },
  users: { icon: Users, color: 'blue' },
  growth: { icon: TrendingUp, color: 'green' }
} as const;

// Marketplace Icons
export const MARKETPLACE_ICONS = {
  free: Heart,
  premium: Crown,
  featured: Sparkles,
  verified: Verified,
  analytics: BarChart3,
  chart: PieChart
} as const;

// Common Button Styles
export const BUTTON_STYLES = {
  primary: "bg-primary text-primary-foreground hover:bg-primary/90",
  secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
  outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
  ghost: "hover:bg-accent hover:text-accent-foreground",
  destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90"
} as const;

// Panel Sizes
export const PANEL_SIZES = {
  sidebar: {
    default: 22,
    min: 18,
    max: 40,
    collapsed: 0
  },
  main: {
    default: 55,
    expanded: 70,
    min: 30
  },
  properties: {
    default: 28,
    min: 22,
    max: 45
  },
  console: {
    default: 30,
    min: 20,
    max: 50
  }
} as const;

// Status Colors
export const STATUS_COLORS = {
  success: {
    bg: 'bg-green-100',
    text: 'text-green-600',
    border: 'border-green-200',
    icon: CheckCircle
  },
  warning: {
    bg: 'bg-yellow-100',
    text: 'text-yellow-600',
    border: 'border-yellow-200',
    icon: AlertTriangle
  },
  error: {
    bg: 'bg-red-100',
    text: 'text-red-600',
    border: 'border-red-200',
    icon: AlertTriangle
  },
  info: {
    bg: 'bg-blue-100',
    text: 'text-blue-600',
    border: 'border-blue-200',
    icon: Clock
  }
} as const;

// Animation Classes
export const ANIMATIONS = {
  fadeIn: "animate-in fade-in-0 duration-200",
  slideIn: "animate-in slide-in-from-left-4 duration-200",
  scaleIn: "animate-in zoom-in-95 duration-200",
  spin: "animate-spin",
  pulse: "animate-pulse",
  bounce: "animate-bounce"
} as const;

// Common Spacing
export const SPACING = {
  xs: "gap-1",
  sm: "gap-2",
  md: "gap-4",
  lg: "gap-6",
  xl: "gap-8"
} as const;

// Helper Functions
export const getCategoryConfig = (category: keyof typeof MODULE_CATEGORIES) => {
  return MODULE_CATEGORIES[category] || MODULE_CATEGORIES.custom;
};

export const getStatusConfig = (status: keyof typeof STATUS_COLORS) => {
  return STATUS_COLORS[status] || STATUS_COLORS.info;
};

export const getMetricConfig = (metric: keyof typeof DASHBOARD_METRICS) => {
  return DASHBOARD_METRICS[metric] || DASHBOARD_METRICS.totalModules;
};

// Common CSS Classes
export const COMMON_CLASSES = {
  card: "rounded-lg border bg-card text-card-foreground shadow-sm",
  cardHeader: "flex flex-col space-y-1.5 p-6",
  cardContent: "p-6 pt-0",
  button: "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",
  input: "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
  badge: "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
} as const;
