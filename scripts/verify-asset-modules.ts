// Verification script to check seeded Asset Modules
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function verifyAssetModules() {
  console.log('🔍 Verifying Asset Module seeding...\n');

  try {
    // Get all asset modules
    const modules = await prisma.assetModule.findMany({
      select: {
        id: true,
        name: true,
        description: true,
        version: true,
        author: true,
        tags: true,
        isActive: true,
        isPublic: true,
        fields: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    console.log(`📦 Found ${modules.length} Asset Modules:\n`);

    modules.forEach((module, index) => {
      console.log(`${index + 1}. ${module.name} (${module.id})`);
      console.log(`   Version: ${module.version}`);
      console.log(`   Author: ${module.author}`);
      console.log(`   Description: ${module.description}`);
      console.log(`   Tags: ${module.tags.join(', ')}`);
      console.log(`   Status: ${module.isActive ? 'Active' : 'Inactive'} | ${module.isPublic ? 'Public' : 'Private'}`);
      
      // Count fields
      const fields = Array.isArray(module.fields) ? module.fields : [];
      console.log(`   Fields: ${fields.length} fields defined`);
      
      console.log(`   Created: ${module.createdAt.toISOString()}`);
      console.log(`   Updated: ${module.updatedAt.toISOString()}`);
      console.log('');
    });

    // Show field details for each module
    console.log('📋 Field Details:\n');
    
    modules.forEach((module) => {
      console.log(`🔧 ${module.name} Fields:`);
      const fields = Array.isArray(module.fields) ? module.fields : [];
      
      if (fields.length > 0) {
        fields.forEach((field: any, index: number) => {
          console.log(`   ${index + 1}. ${field.label || field.name} (${field.type})`);
          if (field.required) console.log(`      - Required: Yes`);
          if (field.description) console.log(`      - Description: ${field.description}`);
          if (field.validation?.options) {
            console.log(`      - Options: ${field.validation.options.slice(0, 3).join(', ')}${field.validation.options.length > 3 ? '...' : ''}`);
          }
        });
      } else {
        console.log('   No fields defined');
      }
      console.log('');
    });

    // Summary statistics
    const totalFields = modules.reduce((sum, module) => {
      const fields = Array.isArray(module.fields) ? module.fields : [];
      return sum + fields.length;
    }, 0);

    const activeModules = modules.filter(m => m.isActive).length;
    const publicModules = modules.filter(m => m.isPublic).length;

    console.log('📊 Summary Statistics:');
    console.log(`   Total Modules: ${modules.length}`);
    console.log(`   Active Modules: ${activeModules}`);
    console.log(`   Public Modules: ${publicModules}`);
    console.log(`   Total Fields: ${totalFields}`);
    console.log(`   Average Fields per Module: ${(totalFields / modules.length).toFixed(1)}`);

    console.log('\n✅ Asset Module verification complete!');

  } catch (error) {
    console.error('❌ Error verifying asset modules:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run verification
verifyAssetModules();
