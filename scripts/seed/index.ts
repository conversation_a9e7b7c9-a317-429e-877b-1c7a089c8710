#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';
import { faker } from '@faker-js/faker';
import { BaseSeeder } from './core/base-seeder';
import { UserSeeder } from './seeders/user-seeder';
import { AssetCategorySeeder } from './seeders/asset-category-seeder';
import { AssetTypeSeeder } from './seeders/asset-type-seeder';
import { AssetSeeder } from './seeders/asset-seeder';
import { AssetModuleSeeder } from './seeders/asset-module-seeder';
import { MaintenanceSeeder } from './seeders/maintenance-seeder';
import { WorkflowSeeder } from './seeders/workflow-seeder';
import { PurchaseOrderSeeder } from './seeders/purchase-order-seeder';
import { LeaseSeeder } from './seeders/lease-seeder';
import { ReportSeeder } from './seeders/report-seeder';
import { NotificationSeeder } from './seeders/notification-seeder';
import { SeedConfig } from './core/seed-config';
import { Logger } from './core/logger';

export class SeedOrchestrator {
  private prisma: PrismaClient;
  private logger: Logger;
  private config: SeedConfig;

  constructor() {
    this.prisma = new PrismaClient();
    this.logger = new Logger();
    this.config = new SeedConfig();
  }

  async run(options?: {
    environment?: 'development' | 'staging' | 'production';
    size?: 'small' | 'medium' | 'large';
    specific?: string[];
    clean?: boolean;
  }): Promise<void> {
    const { environment = 'development', size = 'medium', specific, clean = false } = options || {};
    
    this.logger.info('🌱 Starting database seeding process...');
    this.logger.info(`Environment: ${environment}`);
    this.logger.info(`Size: ${size}`);
    
    if (specific?.length) {
      this.logger.info(`Seeding specific entities: ${specific.join(', ')}`);
    }

    // Set seed for reproducible results
    faker.seed(this.config.getSeed());

    try {
      // Clean database if requested
      if (clean) {
        await this.cleanDatabase();
      }

      // Configure sizes based on environment
      this.config.setEnvironment(environment);
      this.config.setSize(size);

      // Initialize all seeders
      const seeders = this.initializeSeeders();

      // Filter seeders if specific entities requested
      const targetSeeders = specific?.length 
        ? seeders.filter(seeder => specific.includes(seeder.getName()))
        : seeders;

      // Execute seeding in dependency order
      await this.executeSeeding(targetSeeders);

      this.logger.success('🎉 Database seeding completed successfully!');
      
    } catch (error) {
      this.logger.error('❌ Database seeding failed:', error);
      throw error;
    } finally {
      await this.prisma.$disconnect();
    }
  }

  private async cleanDatabase(): Promise<void> {
    this.logger.info('🧹 Cleaning database...');
    
    // Delete in reverse dependency order
    const tables = [
      'AssetModule',
      'AssetLifecycleState',
      'MaintenanceTask',
      'DepreciationSchedule',
      'AssetOperationHistory',
      'AssetTypeForm',
      'FormDefinition',
      'AssetTypeTemplate',
      'DepreciationSettings',
      'MaintenanceSchedule',
      'LifecycleStage',
      'CustomField',
      'AssetType',
      'AssetCategory',
      'LeaseRenewal',
      'PaymentSchedule',
      'LeaseAgreement',
      'AssetDepreciation',
      'AssetDisposal',
      'AssetTransfer',
      'AssetMaintenance',
      'Asset',
      'Report',
      'Notification',
      'ApprovalRequest',
      'Invoice',
      'PurchaseOrder',
      'InventoryCheck',
      'WorkflowExecution',
      'Workflow',
      'User'
    ];

    for (const table of tables) {
      try {
        await this.prisma.$executeRawUnsafe(`DELETE FROM "${table}"`);
        this.logger.debug(`Cleaned table: ${table}`);
      } catch (error: any) {
        if (error.code === 'P2010' || error.meta?.code === '42P01') {
          // Table doesn't exist - skip it
          this.logger.debug(`Table ${table} doesn't exist - skipping`);
        } else {
          this.logger.warn(`Failed to clean table ${table}:`, error.message);
        }
      }
    }

    this.logger.info('✅ Database cleaned successfully');
  }

  private initializeSeeders(): BaseSeeder[] {
    return [
      new UserSeeder(this.prisma, this.config, this.logger),
      new AssetCategorySeeder(this.prisma, this.config, this.logger),
      new AssetTypeSeeder(this.prisma, this.config, this.logger),
      new AssetSeeder(this.prisma, this.config, this.logger),
      new AssetModuleSeeder(this.prisma, this.config, this.logger),
      new MaintenanceSeeder(this.prisma, this.config, this.logger),
      new WorkflowSeeder(this.prisma, this.config, this.logger),
      new PurchaseOrderSeeder(this.prisma, this.config, this.logger),
      new LeaseSeeder(this.prisma, this.config, this.logger),
      new ReportSeeder(this.prisma, this.config, this.logger),
      new NotificationSeeder(this.prisma, this.config, this.logger),
    ];
  }

  private async executeSeeding(seeders: BaseSeeder[]): Promise<void> {
    for (const seeder of seeders) {
      const startTime = Date.now();
      this.logger.info(`📦 Seeding ${seeder.getName()}...`);
      
      try {
        await seeder.seed();
        const duration = Date.now() - startTime;
        this.logger.success(`✅ ${seeder.getName()} seeded in ${duration}ms`);
      } catch (error) {
        this.logger.error(`❌ Failed to seed ${seeder.getName()}:`, error);
        throw error;
      }
    }
  }
}

// CLI execution
if (require.main === module) {
  const args = process.argv.slice(2);
  const options: any = {};

  // Parse CLI arguments
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    switch (arg) {
      case '--environment':
      case '-e':
        options.environment = args[++i];
        break;
      case '--size':
      case '-s':
        options.size = args[++i];
        break;
      case '--specific':
        options.specific = args[++i]?.split(',');
        break;
      case '--clean':
        options.clean = true;
        break;
      case '--help':
      case '-h':
        console.log(`
Usage: tsx scripts/seed/index.ts [options]

Options:
  -e, --environment <env>     Environment (development|staging|production)
  -s, --size <size>          Data size (small|medium|large)
  --specific <entities>      Comma-separated list of specific entities to seed
  --clean                    Clean database before seeding
  -h, --help                 Show this help message

Examples:
  tsx scripts/seed/index.ts --environment development --size medium
  tsx scripts/seed/index.ts --specific users,assets --clean
  tsx scripts/seed/index.ts --environment staging --size large
        `);
        process.exit(0);
        break;
    }
  }

  const orchestrator = new SeedOrchestrator();
  orchestrator.run(options).catch((error) => {
    console.error('Seeding failed:', error);
    process.exit(1);
  });
}
